/*
 * Copyright 2010-2014 NVIDIA Corporation.  All rights reserved.
 *
 * NOTICE TO LICENSEE:
 *
 * This source code and/or documentation ("Licensed Deliverables") are
 * subject to NVIDIA intellectual property rights under U.S. and
 * international Copyright laws.
 *
 * These Licensed Deliverables contained herein is PROPRIETARY and
 * CONFIDENTIAL to NVIDIA and is being provided under the terms and
 * conditions of a form of NVIDIA software license agreement by and
 * between NVIDIA and Licensee ("License Agreement") or electronically
 * accepted by Licensee.  Notwithstanding any terms or conditions to
 * the contrary in the License Agreement, reproduction or disclosure
 * of the Licensed Deliverables to any third party without the express
 * written consent of NVIDIA is prohibited.
 *
 * NOTWITHSTANDING ANY TERMS OR CONDITIONS TO THE CONTRARY IN THE
 * LICENSE AGREEMENT, <PERSON><PERSON>DI<PERSON> MAKES NO REPRESENTATION ABOUT THE
 * SUITABILITY OF THESE LICENSED DELIVERABLES FOR ANY PURPOSE.  IT IS
 * PROVIDED "AS IS" WITHOUT EXPRESS OR IMPLIED WARRANTY OF ANY KIND.
 * NVIDIA DISCLAIMS ALL WARRANTIES WITH REGARD TO THESE LICENSED
 * DELIVERABLES, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY,
 * NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE.
 * NOTWITHSTANDING ANY TERMS OR CONDITIONS TO THE CONTRARY IN THE
 * LICENSE AGREEMENT, IN NO EVENT SHALL NVIDIA BE LIABLE FOR ANY
 * SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, OR ANY
 * DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
 * WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
 * ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE
 * OF THESE LICENSED DELIVERABLES.
 *
 * U.S. Government End Users.  These Licensed Deliverables are a
 * "commercial item" as that term is defined at 48 C.F.R. 2.101 (OCT
 * 1995), consisting of "commercial computer software" and "commercial
 * computer software documentation" as such terms are used in 48
 * C.F.R. 12.212 (SEPT 1995) and is provided to the U.S. Government
 * only as a commercial end item.  Consistent with 48 C.F.R.12.212 and
 * 48 C.F.R. 227.7202-1 through 227.7202-4 (JUNE 1995), all
 * U.S. Government End Users acquire the Licensed Deliverables with
 * only those rights set forth herein.
 *
 * Any use of the Licensed Deliverables in individual and commercial
 * software must include, in the user documentation and internal
 * comments to the code, the above Disclaimer and U.S. Government End
 * Users Notice.
 */

/*
 * Multiple sets of generator parameters for Mersenne Twister
 * with period 2**11213 -1
 */
/*
 * Copyright (c) 2009, 2010 Mutsuo Saito, Makoto Matsumoto and Hiroshima
 * University.  All rights reserved.
 * Copyright (c) 2011 Mutsuo Saito, Makoto Matsumoto, Hiroshima
 * University and University of Tokyo.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above
 *       copyright notice, this list of conditions and the following
 *       disclaimer in the documentation and/or other materials provided
 *       with the distribution.
 *     * Neither the name of the Hiroshima University nor the names of
 *       its contributors may be used to endorse or promote products
 *       derived from this software without specific prior written
 *       permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#if !defined CURAND_MTGP32DC_P_11213_H
#define CURAND_MTGP32DC_P_11213_H

#include "curand_mtgp32.h"


#if (__cplusplus >= 201703L) && defined(__cpp_inline_variables)
inline const int mtgpdc_params_11213_num = 200;
#else
static const int mtgpdc_params_11213_num = 200;
#endif

#if (__cplusplus >= 201703L) && defined(__cpp_inline_variables)
inline mtgp32_params_fast_t mtgp32dc_params_fast_11213[]
#else
static mtgp32_params_fast_t mtgp32dc_params_fast_11213[]
#endif
 = {
    {
        /* No.0 delta:1599 weight:665 */
        11213,
        88,
        19,
        5,
        {(0x00000000),
         (0xaba4d62c),
         (0xbb076f87),
         (0x10a3b9ab),
         (0x22000000),
         (0x89a4d62c),
         (0x99076f87),
         (0x32a3b9ab),
         (0x000095ba),
         (0xaba44396),
         (0xbb07fa3d),
         (0x10a32c11),
         (0x220095ba),
         (0x89a44396),
         (0x9907fa3d),
         (0x32a32c11)},
        {(0x00000000),
         (0x06100000),
         (0x25d80000),
         (0x23c80000),
         (0x282c0000),
         (0x2e3c0000),
         (0x0df40000),
         (0x0be40000),
         (0x3302de00),
         (0x3512de00),
         (0x16dade00),
         (0x10cade00),
         (0x1b2ede00),
         (0x1d3ede00),
         (0x3ef6de00),
         (0x38e6de00)},
        {(0x3f800000),
         (0x3f830800),
         (0x3f92ec00),
         (0x3f91e400),
         (0x3f941600),
         (0x3f971e00),
         (0x3f86fa00),
         (0x3f85f200),
         (0x3f99816f),
         (0x3f9a896f),
         (0x3f8b6d6f),
         (0x3f88656f),
         (0x3f8d976f),
         (0x3f8e9f6f),
         (0x3f9f7b6f),
         (0x3f9c736f)},
        (0xfff80000),
        {0xcb,0xb0,0x3f,0xaa,0x65,0x0d,0xbd,0x1c,0x8c,0xc2,
         0x91,0x00,0x87,0x25,0x7f,0xf8,0x6f,0x23,0xf2,0x18,0x00}
    },
    {
        /* No.1 delta:1185 weight:1129 */
        11213,
        84,
        15,
        12,
        {(0x00000000),
         (0x9a975707),
         (0x75c21b11),
         (0xef554c16),
         (0x15500019),
         (0x8fc7571e),
         (0x60921b08),
         (0xfa054c0f),
         (0x0000d269),
         (0x9a97856e),
         (0x75c2c978),
         (0xef559e7f),
         (0x1550d270),
         (0x8fc78577),
         (0x6092c961),
         (0xfa059e66)},
        {(0x00000000),
         (0x00646c00),
         (0x0810f000),
         (0x08749c00),
         (0x37028000),
         (0x3766ec00),
         (0x3f127000),
         (0x3f761c00),
         (0x00505e00),
         (0x00343200),
         (0x0840ae00),
         (0x0824c200),
         (0x3752de00),
         (0x3736b200),
         (0x3f422e00),
         (0x3f264200)},
        {(0x3f800000),
         (0x3f803236),
         (0x3f840878),
         (0x3f843a4e),
         (0x3f9b8140),
         (0x3f9bb376),
         (0x3f9f8938),
         (0x3f9fbb0e),
         (0x3f80282f),
         (0x3f801a19),
         (0x3f842057),
         (0x3f841261),
         (0x3f9ba96f),
         (0x3f9b9b59),
         (0x3f9fa117),
         (0x3f9f9321)},
        (0xfff80000),
        {0x53,0xe0,0x89,0xe0,0x47,0x42,0x00,0xbd,0xe9,0x04,
         0xc9,0x11,0x5c,0xe3,0x97,0x94,0x94,0xd8,0xbb,0xfc,0x00}
    },
    {
        /* No.2 delta:2848 weight:755 */
        11213,
        25,
        4,
        18,
        {(0x00000000),
         (0x507cbad4),
         (0xe742103b),
         (0xb73eaaef),
         (0x71c00027),
         (0x21bcbaf3),
         (0x9682101c),
         (0xc6feaac8),
         (0x0000dd6c),
         (0x507c67b8),
         (0xe742cd57),
         (0xb73e7783),
         (0x71c0dd4b),
         (0x21bc679f),
         (0x9682cd70),
         (0xc6fe77a4)},
        {(0x00000000),
         (0x04210200),
         (0x08040000),
         (0x0c250200),
         (0x11400000),
         (0x15610200),
         (0x19440000),
         (0x1d650200),
         (0x206f1e00),
         (0x244e1c00),
         (0x286b1e00),
         (0x2c4a1c00),
         (0x312f1e00),
         (0x350e1c00),
         (0x392b1e00),
         (0x3d0a1c00)},
        {(0x3f800000),
         (0x3f821081),
         (0x3f840200),
         (0x3f861281),
         (0x3f88a000),
         (0x3f8ab081),
         (0x3f8ca200),
         (0x3f8eb281),
         (0x3f90378f),
         (0x3f92270e),
         (0x3f94358f),
         (0x3f96250e),
         (0x3f98978f),
         (0x3f9a870e),
         (0x3f9c958f),
         (0x3f9e850e)},
        (0xfff80000),
        {0x9e,0x64,0xa3,0x28,0x10,0x66,0x35,0x72,0xe3,0x07,
         0xf7,0xcf,0x5a,0xe3,0x57,0x6f,0x90,0x0c,0x28,0x79,0x00}
    },
    {
        /* No.3 delta:1100 weight:1261 */
        11213,
        42,
        20,
        9,
        {(0x00000000),
         (0x3ef0ef20),
         (0x9fddd9ea),
         (0xa12d36ca),
         (0x10300036),
         (0x2ec0ef16),
         (0x8fedd9dc),
         (0xb11d36fc),
         (0x00005ced),
         (0x3ef0b3cd),
         (0x9fdd8507),
         (0xa12d6a27),
         (0x10305cdb),
         (0x2ec0b3fb),
         (0x8fed8531),
         (0xb11d6a11)},
        {(0x00000000),
         (0x24470c00),
         (0x00268200),
         (0x24618e00),
         (0x00530000),
         (0x24140c00),
         (0x00758200),
         (0x24328e00),
         (0x105c5e00),
         (0x341b5200),
         (0x107adc00),
         (0x343dd000),
         (0x100f5e00),
         (0x34485200),
         (0x1029dc00),
         (0x346ed000)},
        {(0x3f800000),
         (0x3f922386),
         (0x3f801341),
         (0x3f9230c7),
         (0x3f802980),
         (0x3f920a06),
         (0x3f803ac1),
         (0x3f921947),
         (0x3f882e2f),
         (0x3f9a0da9),
         (0x3f883d6e),
         (0x3f9a1ee8),
         (0x3f8807af),
         (0x3f9a2429),
         (0x3f8814ee),
         (0x3f9a3768)},
        (0xfff80000),
        {0x9b,0x1d,0x8b,0x59,0x56,0x8e,0x7b,0x75,0x24,0x4a,
         0xaf,0x49,0x6e,0xcf,0xcb,0x5d,0x67,0x00,0x31,0xac,0x00}
    },
    {
        /* No.4 delta:5389 weight:1871 */
        11213,
        22,
        1,
        5,
        {(0x00000000),
         (0xb004feb9),
         (0x03b7c894),
         (0xb3b3362d),
         (0x0750004d),
         (0xb754fef4),
         (0x04e7c8d9),
         (0xb4e33660),
         (0x000049e4),
         (0xb004b75d),
         (0x03b78170),
         (0xb3b37fc9),
         (0x075049a9),
         (0xb754b710),
         (0x04e7813d),
         (0xb4e37f84)},
        {(0x00000000),
         (0x48698800),
         (0x04010000),
         (0x4c688800),
         (0x60340000),
         (0x285d8800),
         (0x64350000),
         (0x2c5c8800),
         (0x20065e00),
         (0x686fd600),
         (0x24075e00),
         (0x6c6ed600),
         (0x40325e00),
         (0x085bd600),
         (0x44335e00),
         (0x0c5ad600)},
        {(0x3f800000),
         (0x3fa434c4),
         (0x3f820080),
         (0x3fa63444),
         (0x3fb01a00),
         (0x3f942ec4),
         (0x3fb21a80),
         (0x3f962e44),
         (0x3f90032f),
         (0x3fb437eb),
         (0x3f9203af),
         (0x3fb6376b),
         (0x3fa0192f),
         (0x3f842deb),
         (0x3fa219af),
         (0x3f862d6b)},
        (0xfff80000),
        {0xbb,0x80,0x47,0x21,0x2c,0xbd,0xd4,0x7b,0xf3,0xb3,
         0xbd,0x4d,0x05,0x36,0xf5,0x4d,0xe7,0x89,0xdc,0x11,0x00}
    },
    {
        /* No.5 delta:2280 weight:1243 */
        11213,
        11,
        16,
        1,
        {(0x00000000),
         (0x646a361c),
         (0xe36e4823),
         (0x87047e3f),
         (0x2b20005f),
         (0x4f4a3643),
         (0xc84e487c),
         (0xac247e60),
         (0x0000caac),
         (0x646afcb0),
         (0xe36e828f),
         (0x8704b493),
         (0x2b20caf3),
         (0x4f4afcef),
         (0xc84e82d0),
         (0xac24b4cc)},
        {(0x00000000),
         (0xd49c4200),
         (0x0b98f000),
         (0xdf04b200),
         (0x24007200),
         (0xf09c3000),
         (0x2f988200),
         (0xfb04c000),
         (0x5d311e00),
         (0x89ad5c00),
         (0x56a9ee00),
         (0x8235ac00),
         (0x79316c00),
         (0xadad2e00),
         (0x72a99c00),
         (0xa635de00)},
        {(0x3f800000),
         (0x3fea4e21),
         (0x3f85cc78),
         (0x3fef8259),
         (0x3f920039),
         (0x3ff84e18),
         (0x3f97cc41),
         (0x3ffd8260),
         (0x3fae988f),
         (0x3fc4d6ae),
         (0x3fab54f7),
         (0x3fc11ad6),
         (0x3fbc98b6),
         (0x3fd6d697),
         (0x3fb954ce),
         (0x3fd31aef)},
        (0xfff80000),
        {0x24,0x94,0x81,0x62,0xb4,0x6f,0x10,0x4e,0x66,0xd1,
         0xb4,0xdd,0xa0,0x92,0x2e,0xdf,0x8d,0x6b,0x03,0xa3,0x00}
    },
    {
        /* No.6 delta:722 weight:1357 */
        11213,
        76,
        16,
        6,
        {(0x00000000),
         (0x1bb96b3c),
         (0x7c6323a4),
         (0x67da4898),
         (0x22400061),
         (0x39f96b5d),
         (0x5e2323c5),
         (0x459a48f9),
         (0x00004c61),
         (0x1bb9275d),
         (0x7c636fc5),
         (0x67da04f9),
         (0x22404c00),
         (0x39f9273c),
         (0x5e236fa4),
         (0x459a0498)},
        {(0x00000000),
         (0x08258800),
         (0x1042c000),
         (0x18674800),
         (0x00203000),
         (0x0805b800),
         (0x1062f000),
         (0x18477800),
         (0x70c01e00),
         (0x78e59600),
         (0x6082de00),
         (0x68a75600),
         (0x70e02e00),
         (0x78c5a600),
         (0x60a2ee00),
         (0x68876600)},
        {(0x3f800000),
         (0x3f8412c4),
         (0x3f882160),
         (0x3f8c33a4),
         (0x3f801018),
         (0x3f8402dc),
         (0x3f883178),
         (0x3f8c23bc),
         (0x3fb8600f),
         (0x3fbc72cb),
         (0x3fb0416f),
         (0x3fb453ab),
         (0x3fb87017),
         (0x3fbc62d3),
         (0x3fb05177),
         (0x3fb443b3)},
        (0xfff80000),
        {0x06,0x1a,0x0a,0x3e,0x05,0xb0,0xd2,0xab,0x76,0xef,
         0xe5,0xf6,0xe4,0x09,0x02,0x41,0x4b,0x21,0xef,0x76,0x00}
    },
    {
        /* No.7 delta:3952 weight:825 */
        11213,
        11,
        15,
        16,
        {(0x00000000),
         (0xa49b7ddb),
         (0xbc75e45f),
         (0x18ee9984),
         (0x75600076),
         (0xd1fb7dad),
         (0xc915e429),
         (0x6d8e99f2),
         (0x0000c4f9),
         (0xa49bb922),
         (0xbc7520a6),
         (0x18ee5d7d),
         (0x7560c48f),
         (0xd1fbb954),
         (0xc91520d0),
         (0x6d8e5d0b)},
        {(0x00000000),
         (0xf0015000),
         (0x0a4c2c00),
         (0xfa4d7c00),
         (0x0482b000),
         (0xf483e000),
         (0x0ece9c00),
         (0xfecfcc00),
         (0x45105e00),
         (0xb5110e00),
         (0x4f5c7200),
         (0xbf5d2200),
         (0x4192ee00),
         (0xb193be00),
         (0x4bdec200),
         (0xbbdf9200)},
        {(0x3f800000),
         (0x3ff800a8),
         (0x3f852616),
         (0x3ffd26be),
         (0x3f824158),
         (0x3ffa41f0),
         (0x3f87674e),
         (0x3fff67e6),
         (0x3fa2882f),
         (0x3fda8887),
         (0x3fa7ae39),
         (0x3fdfae91),
         (0x3fa0c977),
         (0x3fd8c9df),
         (0x3fa5ef61),
         (0x3fddefc9)},
        (0xfff80000),
        {0xa9,0x6f,0x34,0xf9,0xdd,0xff,0x0a,0x48,0x3c,0x3f,
         0x3e,0x6f,0x74,0x70,0x37,0x33,0x19,0xe4,0x24,0x9b,0x00}
    },
    {
        /* No.8 delta:4274 weight:1347 */
        11213,
        42,
        6,
        11,
        {(0x00000000),
         (0x311b3c94),
         (0xa1e0c93f),
         (0x90fbf5ab),
         (0x3c600082),
         (0x0d7b3c16),
         (0x9d80c9bd),
         (0xac9bf529),
         (0x0000b622),
         (0x311b8ab6),
         (0xa1e07f1d),
         (0x90fb4389),
         (0x3c60b6a0),
         (0x0d7b8a34),
         (0x9d807f9f),
         (0xac9b430b)},
        {(0x00000000),
         (0x80000000),
         (0x00000000),
         (0x80000000),
         (0xc0000000),
         (0x40000000),
         (0xc0000000),
         (0x40000000),
         (0x20001e00),
         (0xa0001e00),
         (0x20001e00),
         (0xa0001e00),
         (0xe0001e00),
         (0x60001e00),
         (0xe0001e00),
         (0x60001e00)},
        {(0x3f800000),
         (0x3fc00000),
         (0x3f800000),
         (0x3fc00000),
         (0x3fe00000),
         (0x3fa00000),
         (0x3fe00000),
         (0x3fa00000),
         (0x3f90000f),
         (0x3fd0000f),
         (0x3f90000f),
         (0x3fd0000f),
         (0x3ff0000f),
         (0x3fb0000f),
         (0x3ff0000f),
         (0x3fb0000f)},
        (0xfff80000),
        {0x3c,0x5a,0x73,0x0e,0xab,0x68,0x32,0x57,0x6f,0xe6,
         0x1d,0x93,0x6e,0xbd,0xef,0x1a,0x6f,0x25,0x1f,0x70,0x00}
    },
    {
        /* No.9 delta:1902 weight:1001 */
        11213,
        60,
        6,
        11,
        {(0x00000000),
         (0xbc8ed6c0),
         (0x4d9fbb3d),
         (0xf1116dfd),
         (0x8e70009d),
         (0x32fed65d),
         (0xc3efbba0),
         (0x7f616d60),
         (0x0000a00a),
         (0xbc8e76ca),
         (0x4d9f1b37),
         (0xf111cdf7),
         (0x8e70a097),
         (0x32fe7657),
         (0xc3ef1baa),
         (0x7f61cd6a)},
        {(0x00000000),
         (0xc0400400),
         (0x00220000),
         (0xc0620400),
         (0x303e0000),
         (0xf07e0400),
         (0x301c0000),
         (0xf05c0400),
         (0x40021e00),
         (0x80421a00),
         (0x40201e00),
         (0x80601a00),
         (0x703c1e00),
         (0xb07c1a00),
         (0x701e1e00),
         (0xb05e1a00)},
        {(0x3f800000),
         (0x3fe02002),
         (0x3f801100),
         (0x3fe03102),
         (0x3f981f00),
         (0x3ff83f02),
         (0x3f980e00),
         (0x3ff82e02),
         (0x3fa0010f),
         (0x3fc0210d),
         (0x3fa0100f),
         (0x3fc0300d),
         (0x3fb81e0f),
         (0x3fd83e0d),
         (0x3fb80f0f),
         (0x3fd82f0d)},
        (0xfff80000),
        {0x83,0x62,0x6c,0x11,0xe4,0x19,0x2b,0x9b,0x87,0x4d,
         0x8e,0x68,0xde,0x69,0x56,0xc5,0x44,0x33,0x4f,0x20,0x00}
    },
    {
        /* No.10 delta:1148 weight:927 */
        11213,
        45,
        12,
        13,
        {(0x00000000),
         (0x06bc3338),
         (0xdde8b9f9),
         (0xdb548ac1),
         (0xfb2000a5),
         (0xfd9c339d),
         (0x26c8b95c),
         (0x20748a64),
         (0x0000a4ec),
         (0x06bc97d4),
         (0xdde81d15),
         (0xdb542e2d),
         (0xfb20a449),
         (0xfd9c9771),
         (0x26c81db0),
         (0x20742e88)},
        {(0x00000000),
         (0x0c494c00),
         (0x42040c00),
         (0x4e4d4000),
         (0x20090800),
         (0x2c404400),
         (0x620d0400),
         (0x6e444800),
         (0x10409e00),
         (0x1c09d200),
         (0x52449200),
         (0x5e0dde00),
         (0x30499600),
         (0x3c00da00),
         (0x724d9a00),
         (0x7e04d600)},
        {(0x3f800000),
         (0x3f8624a6),
         (0x3fa10206),
         (0x3fa726a0),
         (0x3f900484),
         (0x3f962022),
         (0x3fb10682),
         (0x3fb72224),
         (0x3f88204f),
         (0x3f8e04e9),
         (0x3fa92249),
         (0x3faf06ef),
         (0x3f9824cb),
         (0x3f9e006d),
         (0x3fb926cd),
         (0x3fbf026b)},
        (0xfff80000),
        {0xf8,0x39,0x8a,0x36,0x0f,0x90,0x4f,0x3c,0x04,0x0c,
         0x0a,0x02,0xd4,0x04,0x1b,0xc9,0xc4,0x8e,0x15,0x55,0x00}
    },
    {
        /* No.11 delta:2163 weight:1669 */
        11213,
        80,
        6,
        9,
        {(0x00000000),
         (0x247f444d),
         (0xb70a5ab0),
         (0x93751efd),
         (0xc44000b5),
         (0xe03f44f8),
         (0x734a5a05),
         (0x57351e48),
         (0x0000554f),
         (0x247f1102),
         (0xb70a0fff),
         (0x93754bb2),
         (0xc44055fa),
         (0xe03f11b7),
         (0x734a0f4a),
         (0x57354b07)},
        {(0x00000000),
         (0x30200a00),
         (0x106c0000),
         (0x204c0a00),
         (0x00180400),
         (0x30380e00),
         (0x10740400),
         (0x20540e00),
         (0x100a1e00),
         (0x202a1400),
         (0x00661e00),
         (0x30461400),
         (0x10121a00),
         (0x20321000),
         (0x007e1a00),
         (0x305e1000)},
        {(0x3f800000),
         (0x3f981005),
         (0x3f883600),
         (0x3f902605),
         (0x3f800c02),
         (0x3f981c07),
         (0x3f883a02),
         (0x3f902a07),
         (0x3f88050f),
         (0x3f90150a),
         (0x3f80330f),
         (0x3f98230a),
         (0x3f88090d),
         (0x3f901908),
         (0x3f803f0d),
         (0x3f982f08)},
        (0xfff80000),
        {0x98,0xb0,0x30,0x32,0xcf,0x75,0x63,0xeb,0x3a,0xb6,
         0x4f,0x70,0x47,0x4e,0x6a,0xd1,0x7b,0x40,0xbd,0x90,0x00}
    },
    {
        /* No.12 delta:4985 weight:689 */
        11213,
        81,
        8,
        18,
        {(0x00000000),
         (0xcdcab880),
         (0xdedf1226),
         (0x1315aaa6),
         (0xd17000c6),
         (0x1cbab846),
         (0x0faf12e0),
         (0xc265aa60),
         (0x0000b647),
         (0xcdca0ec7),
         (0xdedfa461),
         (0x13151ce1),
         (0xd170b681),
         (0x1cba0e01),
         (0x0fafa4a7),
         (0xc2651c27)},
        {(0x00000000),
         (0x601c0000),
         (0x08780000),
         (0x68640000),
         (0x00740000),
         (0x60680000),
         (0x080c0000),
         (0x68100000),
         (0x00a01e00),
         (0x60bc1e00),
         (0x08d81e00),
         (0x68c41e00),
         (0x00d41e00),
         (0x60c81e00),
         (0x08ac1e00),
         (0x68b01e00)},
        {(0x3f800000),
         (0x3fb00e00),
         (0x3f843c00),
         (0x3fb43200),
         (0x3f803a00),
         (0x3fb03400),
         (0x3f840600),
         (0x3fb40800),
         (0x3f80500f),
         (0x3fb05e0f),
         (0x3f846c0f),
         (0x3fb4620f),
         (0x3f806a0f),
         (0x3fb0640f),
         (0x3f84560f),
         (0x3fb4580f)},
        (0xfff80000),
        {0x90,0xf2,0x51,0xc2,0x2d,0xc4,0x52,0x73,0x15,0xfa,
         0xeb,0xc3,0x64,0x51,0x48,0xf7,0xb1,0x26,0x38,0xb4,0x00}
    },
    {
        /* No.13 delta:6473 weight:513 */
        11213,
        16,
        1,
        19,
        {(0x00000000),
         (0x2310a4f2),
         (0x91c8cf8c),
         (0xb2d86b7e),
         (0x89c000d1),
         (0xaad0a423),
         (0x1808cf5d),
         (0x3b186baf),
         (0x0000f299),
         (0x2310566b),
         (0x91c83d15),
         (0xb2d899e7),
         (0x89c0f248),
         (0xaad056ba),
         (0x18083dc4),
         (0x3b189936)},
        {(0x00000000),
         (0x4c020000),
         (0xa6800000),
         (0xea820000),
         (0x15000000),
         (0x59020000),
         (0xb3800000),
         (0xff820000),
         (0x29201e00),
         (0x65221e00),
         (0x8fa01e00),
         (0xc3a21e00),
         (0x3c201e00),
         (0x70221e00),
         (0x9aa01e00),
         (0xd6a21e00)},
        {(0x3f800000),
         (0x3fa60100),
         (0x3fd34000),
         (0x3ff54100),
         (0x3f8a8000),
         (0x3fac8100),
         (0x3fd9c000),
         (0x3fffc100),
         (0x3f94900f),
         (0x3fb2910f),
         (0x3fc7d00f),
         (0x3fe1d10f),
         (0x3f9e100f),
         (0x3fb8110f),
         (0x3fcd500f),
         (0x3feb510f)},
        (0xfff80000),
        {0x89,0x7c,0x3c,0xc6,0x3d,0x0a,0xad,0xf2,0xa0,0xf5,
         0x0a,0x4c,0x4e,0x83,0xc5,0x45,0xdb,0xcb,0xb5,0x98,0x00}
    },
    {
        /* No.14 delta:1389 weight:725 */
        11213,
        63,
        14,
        18,
        {(0x00000000),
         (0x81c7909c),
         (0xecb46b0c),
         (0x6d73fb90),
         (0xf33000ef),
         (0x72f79073),
         (0x1f846be3),
         (0x9e43fb7f),
         (0x0000e902),
         (0x81c7799e),
         (0xecb4820e),
         (0x6d731292),
         (0xf330e9ed),
         (0x72f77971),
         (0x1f8482e1),
         (0x9e43127d)},
        {(0x00000000),
         (0x09001000),
         (0x02010000),
         (0x0b011000),
         (0x00201800),
         (0x09200800),
         (0x02211800),
         (0x0b210800),
         (0x30039e00),
         (0x39038e00),
         (0x32029e00),
         (0x3b028e00),
         (0x30238600),
         (0x39239600),
         (0x32228600),
         (0x3b229600)},
        {(0x3f800000),
         (0x3f848008),
         (0x3f810080),
         (0x3f858088),
         (0x3f80100c),
         (0x3f849004),
         (0x3f81108c),
         (0x3f859084),
         (0x3f9801cf),
         (0x3f9c81c7),
         (0x3f99014f),
         (0x3f9d8147),
         (0x3f9811c3),
         (0x3f9c91cb),
         (0x3f991143),
         (0x3f9d914b)},
        (0xfff80000),
        {0xb7,0x0b,0xee,0xcb,0xc6,0x29,0x7c,0xe3,0xc5,0x58,
         0x71,0xe7,0x4b,0xe5,0xc9,0x63,0xbd,0xa4,0x75,0xc7,0x00}
    },
    {
        /* No.15 delta:8365 weight:1135 */
        11213,
        38,
        28,
        1,
        {(0x00000000),
         (0xe56c7b3b),
         (0x759d951b),
         (0x90f1ee20),
         (0x1a6000fa),
         (0xff0c7bc1),
         (0x6ffd95e1),
         (0x8a91eeda),
         (0x00004474),
         (0xe56c3f4f),
         (0x759dd16f),
         (0x90f1aa54),
         (0x1a60448e),
         (0xff0c3fb5),
         (0x6ffdd195),
         (0x8a91aaae)},
        {(0x00000000),
         (0xc0410a00),
         (0xe70c0400),
         (0x274d0e00),
         (0xfa3e8000),
         (0x3a7f8a00),
         (0x1d328400),
         (0xdd738e00),
         (0x20801e00),
         (0xe0c11400),
         (0xc78c1a00),
         (0x07cd1000),
         (0xdabe9e00),
         (0x1aff9400),
         (0x3db29a00),
         (0xfdf39000)},
        {(0x3f800000),
         (0x3fe02085),
         (0x3ff38602),
         (0x3f93a687),
         (0x3ffd1f40),
         (0x3f9d3fc5),
         (0x3f8e9942),
         (0x3feeb9c7),
         (0x3f90400f),
         (0x3ff0608a),
         (0x3fe3c60d),
         (0x3f83e688),
         (0x3fed5f4f),
         (0x3f8d7fca),
         (0x3f9ed94d),
         (0x3ffef9c8)},
        (0xfff80000),
        {0xd2,0xa4,0x8f,0xf8,0x54,0x0f,0xe3,0x14,0x17,0xeb,
         0x17,0x78,0x41,0x78,0x69,0xbb,0x4b,0x01,0x85,0x8e,0x00}
    },
    {
        /* No.16 delta:6253 weight:641 */
        11213,
        3,
        30,
        2,
        {(0x00000000),
         (0x83a2a3cb),
         (0x52e0a64c),
         (0xd1420587),
         (0x9850010b),
         (0x1bf2a2c0),
         (0xcab0a747),
         (0x4912048c),
         (0x0000aaa5),
         (0x83a2096e),
         (0x52e00ce9),
         (0xd142af22),
         (0x9850abae),
         (0x1bf20865),
         (0xcab00de2),
         (0x4912ae29)},
        {(0x00000000),
         (0xe9100000),
         (0x94d00000),
         (0x7dc00000),
         (0xdbcc0000),
         (0x32dc0000),
         (0x4f1c0000),
         (0xa60c0000),
         (0x09d01e00),
         (0xe0c01e00),
         (0x9d001e00),
         (0x74101e00),
         (0xd21c1e00),
         (0x3b0c1e00),
         (0x46cc1e00),
         (0xafdc1e00)},
        {(0x3f800000),
         (0x3ff48800),
         (0x3fca6800),
         (0x3fbee000),
         (0x3fede600),
         (0x3f996e00),
         (0x3fa78e00),
         (0x3fd30600),
         (0x3f84e80f),
         (0x3ff0600f),
         (0x3fce800f),
         (0x3fba080f),
         (0x3fe90e0f),
         (0x3f9d860f),
         (0x3fa3660f),
         (0x3fd7ee0f)},
        (0xfff80000),
        {0xa6,0xc6,0x12,0x64,0xaf,0x79,0x40,0x50,0x7b,0xb9,
         0xa9,0xfa,0x9a,0xc0,0x7a,0xaf,0x63,0x83,0x39,0x6c,0x00}
    },
    {
        /* No.17 delta:5329 weight:467 */
        11213,
        55,
        1,
        16,
        {(0x00000000),
         (0x2ce39cc8),
         (0x6312ea87),
         (0x4ff1764f),
         (0x9ef00113),
         (0xb2139ddb),
         (0xfde2eb94),
         (0xd101775c),
         (0x00007ddc),
         (0x2ce3e114),
         (0x6312975b),
         (0x4ff10b93),
         (0x9ef07ccf),
         (0xb213e007),
         (0xfde29648),
         (0xd1010a80)},
        {(0x00000000),
         (0x0c021000),
         (0xe3400000),
         (0xef421000),
         (0x02000000),
         (0x0e021000),
         (0xe1400000),
         (0xed421000),
         (0x50a01e00),
         (0x5ca20e00),
         (0xb3e01e00),
         (0xbfe20e00),
         (0x52a01e00),
         (0x5ea20e00),
         (0xb1e01e00),
         (0xbde20e00)},
        {(0x3f800000),
         (0x3f860108),
         (0x3ff1a000),
         (0x3ff7a108),
         (0x3f810000),
         (0x3f870108),
         (0x3ff0a000),
         (0x3ff6a108),
         (0x3fa8500f),
         (0x3fae5107),
         (0x3fd9f00f),
         (0x3fdff107),
         (0x3fa9500f),
         (0x3faf5107),
         (0x3fd8f00f),
         (0x3fdef107)},
        (0xfff80000),
        {0xe4,0xb8,0x1d,0x0f,0x15,0xe2,0xd6,0xd2,0x30,0x67,
         0x18,0x06,0x38,0xd0,0x63,0x09,0x4d,0xc1,0xaa,0x48,0x00}
    },
    {
        /* No.18 delta:3046 weight:699 */
        11213,
        9,
        9,
        15,
        {(0x00000000),
         (0x228294e5),
         (0xc1861e7b),
         (0xe3048a9e),
         (0x6250012a),
         (0x40d295cf),
         (0xa3d61f51),
         (0x81548bb4),
         (0x00005800),
         (0x2282cce5),
         (0xc186467b),
         (0xe304d29e),
         (0x6250592a),
         (0x40d2cdcf),
         (0xa3d64751),
         (0x8154d3b4)},
        {(0x00000000),
         (0x57910c00),
         (0xd07cc400),
         (0x87edc800),
         (0xa8036800),
         (0xff926400),
         (0x787fac00),
         (0x2feea000),
         (0x00521e00),
         (0x57c31200),
         (0xd02eda00),
         (0x87bfd600),
         (0xa8517600),
         (0xffc07a00),
         (0x782db200),
         (0x2fbcbe00)},
        {(0x3f800000),
         (0x3fabc886),
         (0x3fe83e62),
         (0x3fc3f6e4),
         (0x3fd401b4),
         (0x3fffc932),
         (0x3fbc3fd6),
         (0x3f97f750),
         (0x3f80290f),
         (0x3fabe189),
         (0x3fe8176d),
         (0x3fc3dfeb),
         (0x3fd428bb),
         (0x3fffe03d),
         (0x3fbc16d9),
         (0x3f97de5f)},
        (0xfff80000),
        {0x3d,0xb7,0x41,0x11,0xf8,0x6e,0xe6,0x89,0x71,0x95,
         0x3b,0x7c,0x3a,0x38,0x60,0x12,0x4c,0xec,0xaa,0x83,0x00}
    },
    {
        /* No.19 delta:786 weight:1431 */
        11213,
        75,
        17,
        6,
        {(0x00000000),
         (0x819ee5dc),
         (0x4ffaa079),
         (0xce6445a5),
         (0xe6300137),
         (0x67aee4eb),
         (0xa9caa14e),
         (0x28544492),
         (0x00008a74),
         (0x819e6fa8),
         (0x4ffa2a0d),
         (0xce64cfd1),
         (0xe6308b43),
         (0x67ae6e9f),
         (0xa9ca2b3a),
         (0x2854cee6)},
        {(0x00000000),
         (0x98018000),
         (0x20039800),
         (0xb8021800),
         (0x40104000),
         (0xd811c000),
         (0x6013d800),
         (0xf8125800),
         (0x00033e00),
         (0x9802be00),
         (0x2000a600),
         (0xb8012600),
         (0x40137e00),
         (0xd812fe00),
         (0x6010e600),
         (0xf8116600)},
        {(0x3f800000),
         (0x3fcc00c0),
         (0x3f9001cc),
         (0x3fdc010c),
         (0x3fa00820),
         (0x3fec08e0),
         (0x3fb009ec),
         (0x3ffc092c),
         (0x3f80019f),
         (0x3fcc015f),
         (0x3f900053),
         (0x3fdc0093),
         (0x3fa009bf),
         (0x3fec097f),
         (0x3fb00873),
         (0x3ffc08b3)},
        (0xfff80000),
        {0x90,0xc1,0xc9,0xe7,0xea,0xaf,0xd9,0x5b,0x1f,0x93,
         0x8d,0xcd,0x9b,0xf4,0xbb,0x91,0x58,0xb2,0x03,0x70,0x00}
    },
    {
        /* No.20 delta:846 weight:909 */
        11213,
        70,
        15,
        6,
        {(0x00000000),
         (0xc56d80c5),
         (0x4bb862e9),
         (0x8ed5e22c),
         (0x92e00143),
         (0x578d8186),
         (0xd95863aa),
         (0x1c35e36f),
         (0x000095b5),
         (0xc56d1570),
         (0x4bb8f75c),
         (0x8ed57799),
         (0x92e094f6),
         (0x578d1433),
         (0xd958f61f),
         (0x1c3576da)},
        {(0x00000000),
         (0x20642000),
         (0x2530d800),
         (0x0554f800),
         (0x01a05200),
         (0x21c47200),
         (0x24908a00),
         (0x04f4aa00),
         (0x00521e00),
         (0x20363e00),
         (0x2562c600),
         (0x0506e600),
         (0x01f24c00),
         (0x21966c00),
         (0x24c29400),
         (0x04a6b400)},
        {(0x3f800000),
         (0x3f903210),
         (0x3f92986c),
         (0x3f82aa7c),
         (0x3f80d029),
         (0x3f90e239),
         (0x3f924845),
         (0x3f827a55),
         (0x3f80290f),
         (0x3f901b1f),
         (0x3f92b163),
         (0x3f828373),
         (0x3f80f926),
         (0x3f90cb36),
         (0x3f92614a),
         (0x3f82535a)},
        (0xfff80000),
        {0xd6,0xb1,0xe1,0x27,0x40,0x54,0x48,0x67,0x71,0xe5,
         0x2a,0x38,0xbf,0xa7,0x93,0x82,0x17,0xcd,0x71,0x33,0x00}
    },
    {
        /* No.21 delta:1909 weight:729 */
        11213,
        63,
        15,
        17,
        {(0x00000000),
         (0x3c495454),
         (0xf3a1ddf0),
         (0xcfe889a4),
         (0x1b200159),
         (0x2769550d),
         (0xe881dca9),
         (0xd4c888fd),
         (0x0000cd3b),
         (0x3c49996f),
         (0xf3a110cb),
         (0xcfe8449f),
         (0x1b20cc62),
         (0x27699836),
         (0xe8811192),
         (0xd4c845c6)},
        {(0x00000000),
         (0x08027000),
         (0x9651d400),
         (0x9e53a400),
         (0x0040a400),
         (0x0842d400),
         (0x96117000),
         (0x9e130000),
         (0x006e9e00),
         (0x086cee00),
         (0x963f4a00),
         (0x9e3d3a00),
         (0x002e3a00),
         (0x082c4a00),
         (0x967fee00),
         (0x9e7d9e00)},
        {(0x3f800000),
         (0x3f840138),
         (0x3fcb28ea),
         (0x3fcf29d2),
         (0x3f802052),
         (0x3f84216a),
         (0x3fcb08b8),
         (0x3fcf0980),
         (0x3f80374f),
         (0x3f843677),
         (0x3fcb1fa5),
         (0x3fcf1e9d),
         (0x3f80171d),
         (0x3f841625),
         (0x3fcb3ff7),
         (0x3fcf3ecf)},
        (0xfff80000),
        {0xc8,0xdc,0x70,0x0a,0x23,0xa5,0x20,0x35,0xa2,0xd7,
         0xd1,0x53,0xfc,0xe0,0xc1,0xde,0xc0,0x2b,0x65,0x4d,0x00}
    },
    {
        /* No.22 delta:2420 weight:985 */
        11213,
        32,
        7,
        15,
        {(0x00000000),
         (0xd25d9ecf),
         (0xb1288d77),
         (0x637513b8),
         (0xc3700160),
         (0x112d9faf),
         (0x72588c17),
         (0xa00512d8),
         (0x000093d5),
         (0xd25d0d1a),
         (0xb1281ea2),
         (0x6375806d),
         (0xc37092b5),
         (0x112d0c7a),
         (0x72581fc2),
         (0xa005810d)},
        {(0x00000000),
         (0x10134200),
         (0x028b0000),
         (0x12984200),
         (0x50480000),
         (0x405b4200),
         (0x52c30000),
         (0x42d04200),
         (0x10ee5e00),
         (0x00fd1c00),
         (0x12655e00),
         (0x02761c00),
         (0x40a65e00),
         (0x50b51c00),
         (0x422d5e00),
         (0x523e1c00)},
        {(0x3f800000),
         (0x3f8809a1),
         (0x3f814580),
         (0x3f894c21),
         (0x3fa82400),
         (0x3fa02da1),
         (0x3fa96180),
         (0x3fa16821),
         (0x3f88772f),
         (0x3f807e8e),
         (0x3f8932af),
         (0x3f813b0e),
         (0x3fa0532f),
         (0x3fa85a8e),
         (0x3fa116af),
         (0x3fa91f0e)},
        (0xfff80000),
        {0xd0,0xc2,0xd1,0x3e,0x09,0x41,0xc7,0x9c,0x09,0x33,
         0x25,0x5f,0x84,0x21,0xc4,0xcc,0xc5,0x43,0x2a,0x14,0x00}
    },
    {
        /* No.23 delta:985 weight:863 */
        11213,
        70,
        12,
        10,
        {(0x00000000),
         (0x6e6163b2),
         (0xa3cecdea),
         (0xcdafae58),
         (0xb670017d),
         (0xd81162cf),
         (0x15becc97),
         (0x7bdfaf25),
         (0x0000d554),
         (0x6e61b6e6),
         (0xa3ce18be),
         (0xcdaf7b0c),
         (0xb670d429),
         (0xd811b79b),
         (0x15be19c3),
         (0x7bdf7a71)},
        {(0x00000000),
         (0x0338d800),
         (0x00545800),
         (0x036c8000),
         (0x01521000),
         (0x026ac800),
         (0x01064800),
         (0x023e9000),
         (0x0020de00),
         (0x03180600),
         (0x00748600),
         (0x034c5e00),
         (0x0172ce00),
         (0x024a1600),
         (0x01269600),
         (0x021e4e00)},
        {(0x3f800000),
         (0x3f819c6c),
         (0x3f802a2c),
         (0x3f81b640),
         (0x3f80a908),
         (0x3f813564),
         (0x3f808324),
         (0x3f811f48),
         (0x3f80106f),
         (0x3f818c03),
         (0x3f803a43),
         (0x3f81a62f),
         (0x3f80b967),
         (0x3f81250b),
         (0x3f80934b),
         (0x3f810f27)},
        (0xfff80000),
        {0x57,0xda,0x1e,0x1e,0x0f,0xc1,0x5c,0xac,0x32,0x8f,
         0xe4,0x94,0x24,0xf0,0x86,0x24,0x1b,0xde,0x44,0x16,0x00}
    },
    {
        /* No.24 delta:1573 weight:1043 */
        11213,
        58,
        21,
        2,
        {(0x00000000),
         (0xa6c80c3d),
         (0x3502d818),
         (0x93cad425),
         (0xda700189),
         (0x7cb80db4),
         (0xef72d991),
         (0x49bad5ac),
         (0x0000cb8c),
         (0xa6c8c7b1),
         (0x35021394),
         (0x93ca1fa9),
         (0xda70ca05),
         (0x7cb8c638),
         (0xef72121d),
         (0x49ba1e20)},
        {(0x00000000),
         (0xd66f0400),
         (0x014ca600),
         (0xd723a200),
         (0x00442a00),
         (0xd62b2e00),
         (0x01088c00),
         (0xd7678800),
         (0x0c835e00),
         (0xdaec5a00),
         (0x0dcff800),
         (0xdba0fc00),
         (0x0cc77400),
         (0xdaa87000),
         (0x0d8bd200),
         (0xdbe4d600)},
        {(0x3f800000),
         (0x3feb3782),
         (0x3f80a653),
         (0x3feb91d1),
         (0x3f802215),
         (0x3feb1597),
         (0x3f808446),
         (0x3febb3c4),
         (0x3f8641af),
         (0x3fed762d),
         (0x3f86e7fc),
         (0x3fedd07e),
         (0x3f8663ba),
         (0x3fed5438),
         (0x3f86c5e9),
         (0x3fedf26b)},
        (0xfff80000),
        {0xd3,0x5b,0x43,0x3f,0x00,0x55,0xf8,0x40,0xfe,0x8f,
         0xd0,0xe0,0xac,0x48,0x50,0xf2,0x50,0x2a,0xb7,0xcf,0x00}
    },
    {
        /* No.25 delta:1860 weight:1169 */
        11213,
        33,
        7,
        10,
        {(0x00000000),
         (0x3f9ccf17),
         (0xcdd7a2a1),
         (0xf24b6db6),
         (0xf9c00195),
         (0xc65cce82),
         (0x3417a334),
         (0x0b8b6c23),
         (0x00006c32),
         (0x3f9ca325),
         (0xcdd7ce93),
         (0xf24b0184),
         (0xf9c06da7),
         (0xc65ca2b0),
         (0x3417cf06),
         (0x0b8b0011)},
        {(0x00000000),
         (0x043a0400),
         (0x00714000),
         (0x044b4400),
         (0x10408400),
         (0x147a8000),
         (0x1031c400),
         (0x140bc000),
         (0x70049e00),
         (0x743e9a00),
         (0x7075de00),
         (0x744fda00),
         (0x60441a00),
         (0x647e1e00),
         (0x60355a00),
         (0x640f5e00)},
        {(0x3f800000),
         (0x3f821d02),
         (0x3f8038a0),
         (0x3f8225a2),
         (0x3f882042),
         (0x3f8a3d40),
         (0x3f8818e2),
         (0x3f8a05e0),
         (0x3fb8024f),
         (0x3fba1f4d),
         (0x3fb83aef),
         (0x3fba27ed),
         (0x3fb0220d),
         (0x3fb23f0f),
         (0x3fb01aad),
         (0x3fb207af)},
        (0xfff80000),
        {0x6f,0x6a,0x15,0x14,0x9e,0x4e,0xca,0xcd,0xe1,0xfb,
         0x26,0x23,0xa0,0xa9,0x21,0x22,0xca,0x8c,0x68,0x8f,0x00}
    },
    {
        /* No.26 delta:2274 weight:1019 */
        11213,
        18,
        7,
        13,
        {(0x00000000),
         (0x83f4f835),
         (0x34e8f3fa),
         (0xb71c0bcf),
         (0xf21001a3),
         (0x71e4f996),
         (0xc6f8f259),
         (0x450c0a6c),
         (0x00009d49),
         (0x83f4657c),
         (0x34e86eb3),
         (0xb71c9686),
         (0xf2109cea),
         (0x71e464df),
         (0xc6f86f10),
         (0x450c9725)},
        {(0x00000000),
         (0x69050000),
         (0x0b0a1800),
         (0x620f1800),
         (0x41a06000),
         (0x28a56000),
         (0x4aaa7800),
         (0x23af7800),
         (0x046b1e00),
         (0x6d6e1e00),
         (0x0f610600),
         (0x66640600),
         (0x45cb7e00),
         (0x2cce7e00),
         (0x4ec16600),
         (0x27c46600)},
        {(0x3f800000),
         (0x3fb48280),
         (0x3f85850c),
         (0x3fb1078c),
         (0x3fa0d030),
         (0x3f9452b0),
         (0x3fa5553c),
         (0x3f91d7bc),
         (0x3f82358f),
         (0x3fb6b70f),
         (0x3f87b083),
         (0x3fb33203),
         (0x3fa2e5bf),
         (0x3f96673f),
         (0x3fa760b3),
         (0x3f93e233)},
        (0xfff80000),
        {0x6c,0xda,0xe3,0x3d,0xac,0x72,0x40,0x41,0xca,0xa3,
         0xf7,0xdf,0xff,0x2d,0x57,0xea,0x1f,0x8d,0x6c,0x04,0x00}
    },
    {
        /* No.27 delta:2503 weight:711 */
        11213,
        9,
        12,
        13,
        {(0x00000000),
         (0xc54f7f87),
         (0x6904a950),
         (0xac4bd6d7),
         (0x85c001bc),
         (0x408f7e3b),
         (0xecc4a8ec),
         (0x298bd76b),
         (0x00007b77),
         (0xc54f04f0),
         (0x6904d227),
         (0xac4bada0),
         (0x85c07acb),
         (0x408f054c),
         (0xecc4d39b),
         (0x298bac1c)},
        {(0x00000000),
         (0xc7002c00),
         (0x5a61c800),
         (0x9d61e400),
         (0x12013800),
         (0xd5011400),
         (0x4860f000),
         (0x8f60dc00),
         (0xc8c01e00),
         (0x0fc03200),
         (0x92a1d600),
         (0x55a1fa00),
         (0xdac12600),
         (0x1dc10a00),
         (0x80a0ee00),
         (0x47a0c200)},
        {(0x3f800000),
         (0x3fe38016),
         (0x3fad30e4),
         (0x3fceb0f2),
         (0x3f89009c),
         (0x3fea808a),
         (0x3fa43078),
         (0x3fc7b06e),
         (0x3fe4600f),
         (0x3f87e019),
         (0x3fc950eb),
         (0x3faad0fd),
         (0x3fed6093),
         (0x3f8ee085),
         (0x3fc05077),
         (0x3fa3d061)},
        (0xfff80000),
        {0xd6,0xd3,0xee,0x8e,0x9a,0x94,0x64,0x4c,0x3a,0x7b,
         0x1d,0x2f,0x64,0xef,0x67,0x33,0x0d,0x49,0x2e,0x20,0x00}
    },
    {
        /* No.28 delta:1642 weight:1597 */
        11213,
        14,
        16,
        3,
        {(0x00000000),
         (0x8147d21a),
         (0x627aba0f),
         (0xe33d6815),
         (0x279001c1),
         (0xa6d7d3db),
         (0x45eabbce),
         (0xc4ad69d4),
         (0x0000f4d1),
         (0x814726cb),
         (0x627a4ede),
         (0xe33d9cc4),
         (0x2790f510),
         (0xa6d7270a),
         (0x45ea4f1f),
         (0xc4ad9d05)},
        {(0x00000000),
         (0x210c6a00),
         (0x0071b800),
         (0x217dd200),
         (0xcc021a00),
         (0xed0e7000),
         (0xcc73a200),
         (0xed7fc800),
         (0x41b2de00),
         (0x60beb400),
         (0x41c36600),
         (0x60cf0c00),
         (0x8db0c400),
         (0xacbcae00),
         (0x8dc17c00),
         (0xaccd1600)},
        {(0x3f800000),
         (0x3f908635),
         (0x3f8038dc),
         (0x3f90bee9),
         (0x3fe6010d),
         (0x3ff68738),
         (0x3fe639d1),
         (0x3ff6bfe4),
         (0x3fa0d96f),
         (0x3fb05f5a),
         (0x3fa0e1b3),
         (0x3fb06786),
         (0x3fc6d862),
         (0x3fd65e57),
         (0x3fc6e0be),
         (0x3fd6668b)},
        (0xfff80000),
        {0x58,0xce,0x35,0x39,0xc0,0x04,0xad,0xd6,0xdd,0xb7,
         0x5d,0xf1,0xac,0xf9,0x85,0xea,0x3f,0x74,0x52,0x6d,0x00}
    },
    {
        /* No.29 delta:1888 weight:1763 */
        11213,
        91,
        4,
        2,
        {(0x00000000),
         (0xa620066e),
         (0x498ca8fb),
         (0xefacae95),
         (0xc3f001d3),
         (0x65d007bd),
         (0x8a7ca928),
         (0x2c5caf46),
         (0x0000927c),
         (0xa6209412),
         (0x498c3a87),
         (0xefac3ce9),
         (0xc3f093af),
         (0x65d095c1),
         (0x8a7c3b54),
         (0x2c5c3d3a)},
        {(0x00000000),
         (0x29909200),
         (0x0074c600),
         (0x29e45400),
         (0x400e7000),
         (0x699ee200),
         (0x407ab600),
         (0x69ea2400),
         (0x44029e00),
         (0x6d920c00),
         (0x44765800),
         (0x6de6ca00),
         (0x040cee00),
         (0x2d9c7c00),
         (0x04782800),
         (0x2de8ba00)},
        {(0x3f800000),
         (0x3f94c849),
         (0x3f803a63),
         (0x3f94f22a),
         (0x3fa00738),
         (0x3fb4cf71),
         (0x3fa03d5b),
         (0x3fb4f512),
         (0x3fa2014f),
         (0x3fb6c906),
         (0x3fa23b2c),
         (0x3fb6f365),
         (0x3f820677),
         (0x3f96ce3e),
         (0x3f823c14),
         (0x3f96f45d)},
        (0xfff80000),
        {0xbf,0x14,0xa8,0x90,0x29,0x75,0x15,0xde,0x68,0x12,
         0x39,0xb3,0x6e,0x2f,0xcb,0x19,0x1f,0xe6,0x2c,0x9b,0x00}
    },
    {
        /* No.30 delta:1194 weight:873 */
        11213,
        90,
        10,
        14,
        {(0x00000000),
         (0xdd216696),
         (0x738a5c01),
         (0xaeab3a97),
         (0x402001ec),
         (0x9d01677a),
         (0x33aa5ded),
         (0xee8b3b7b),
         (0x0000c5c9),
         (0xdd21a35f),
         (0x738a99c8),
         (0xaeabff5e),
         (0x4020c425),
         (0x9d01a2b3),
         (0x33aa9824),
         (0xee8bfeb2)},
        {(0x00000000),
         (0x193a0000),
         (0x07a49000),
         (0x1e9e9000),
         (0x00658800),
         (0x195f8800),
         (0x07c11800),
         (0x1efb1800),
         (0x4456fe00),
         (0x5d6cfe00),
         (0x43f26e00),
         (0x5ac86e00),
         (0x44337600),
         (0x5d097600),
         (0x4397e600),
         (0x5aade600)},
        {(0x3f800000),
         (0x3f8c9d00),
         (0x3f83d248),
         (0x3f8f4f48),
         (0x3f8032c4),
         (0x3f8cafc4),
         (0x3f83e08c),
         (0x3f8f7d8c),
         (0x3fa22b7f),
         (0x3faeb67f),
         (0x3fa1f937),
         (0x3fad6437),
         (0x3fa219bb),
         (0x3fae84bb),
         (0x3fa1cbf3),
         (0x3fad56f3)},
        (0xfff80000),
        {0x45,0xbe,0xc0,0x65,0xca,0x28,0x44,0x0e,0x06,0x4e,
         0xa0,0xbf,0x48,0xa8,0x8e,0x39,0x64,0x68,0xe1,0x30,0x00}
    },
    {
        /* No.31 delta:1509 weight:1313 */
        11213,
        86,
        6,
        7,
        {(0x00000000),
         (0x41d73f34),
         (0x0930d7f4),
         (0x48e7e8c0),
         (0x65b001f9),
         (0x24673ecd),
         (0x6c80d60d),
         (0x2d57e939),
         (0x00008ad2),
         (0x41d7b5e6),
         (0x09305d26),
         (0x48e76212),
         (0x65b08b2b),
         (0x2467b41f),
         (0x6c805cdf),
         (0x2d5763eb)},
        {(0x00000000),
         (0x02010000),
         (0x10332000),
         (0x12322000),
         (0x10030000),
         (0x12020000),
         (0x00302000),
         (0x02312000),
         (0x403a9e00),
         (0x423b9e00),
         (0x5009be00),
         (0x5208be00),
         (0x50399e00),
         (0x52389e00),
         (0x400abe00),
         (0x420bbe00)},
        {(0x3f800000),
         (0x3f810080),
         (0x3f881990),
         (0x3f891910),
         (0x3f880180),
         (0x3f890100),
         (0x3f801810),
         (0x3f811890),
         (0x3fa01d4f),
         (0x3fa11dcf),
         (0x3fa804df),
         (0x3fa9045f),
         (0x3fa81ccf),
         (0x3fa91c4f),
         (0x3fa0055f),
         (0x3fa105df)},
        (0xfff80000),
        {0x9f,0xb2,0x3a,0xbb,0xab,0xbd,0xc1,0x43,0x82,0x94,
         0x1f,0x8e,0xdf,0x89,0xbb,0xf6,0xee,0x6a,0x10,0x54,0x00}
    },
    {
        /* No.32 delta:3454 weight:869 */
        11213,
        36,
        3,
        15,
        {(0x00000000),
         (0x1709ea04),
         (0x8e9b8c43),
         (0x99926647),
         (0xdd100203),
         (0xca19e807),
         (0x538b8e40),
         (0x44826444),
         (0x0000f3b3),
         (0x170919b7),
         (0x8e9b7ff0),
         (0x999295f4),
         (0xdd10f1b0),
         (0xca191bb4),
         (0x538b7df3),
         (0x448297f7)},
        {(0x00000000),
         (0x28228000),
         (0x1fc10000),
         (0x37e38000),
         (0x00400000),
         (0x28628000),
         (0x1f810000),
         (0x37a38000),
         (0x12b01e00),
         (0x3a929e00),
         (0x0d711e00),
         (0x25539e00),
         (0x12f01e00),
         (0x3ad29e00),
         (0x0d311e00),
         (0x25139e00)},
        {(0x3f800000),
         (0x3f941140),
         (0x3f8fe080),
         (0x3f9bf1c0),
         (0x3f802000),
         (0x3f943140),
         (0x3f8fc080),
         (0x3f9bd1c0),
         (0x3f89580f),
         (0x3f9d494f),
         (0x3f86b88f),
         (0x3f92a9cf),
         (0x3f89780f),
         (0x3f9d694f),
         (0x3f86988f),
         (0x3f9289cf)},
        (0xfff80000),
        {0xbd,0x88,0xcf,0x70,0x9b,0x4f,0x1b,0xe7,0xe6,0xab,
         0x62,0x27,0x66,0xaf,0x36,0xbb,0x10,0x8b,0x27,0xd7,0x00}
    },
    {
        /* No.33 delta:1105 weight:1263 */
        11213,
        40,
        9,
        3,
        {(0x00000000),
         (0x4406f6da),
         (0xf592d5cb),
         (0xb1942311),
         (0xe0900211),
         (0xa496f4cb),
         (0x1502d7da),
         (0x51042100),
         (0x0000075b),
         (0x4406f181),
         (0xf592d290),
         (0xb194244a),
         (0xe090054a),
         (0xa496f390),
         (0x1502d081),
         (0x5104265b)},
        {(0x00000000),
         (0x10151000),
         (0x10640000),
         (0x00711000),
         (0x20081800),
         (0x301d0800),
         (0x306c1800),
         (0x20790800),
         (0x00025e00),
         (0x10174e00),
         (0x10665e00),
         (0x00734e00),
         (0x200a4600),
         (0x301f5600),
         (0x306e4600),
         (0x207b5600)},
        {(0x3f800000),
         (0x3f880a88),
         (0x3f883200),
         (0x3f803888),
         (0x3f90040c),
         (0x3f980e84),
         (0x3f98360c),
         (0x3f903c84),
         (0x3f80012f),
         (0x3f880ba7),
         (0x3f88332f),
         (0x3f8039a7),
         (0x3f900523),
         (0x3f980fab),
         (0x3f983723),
         (0x3f903dab)},
        (0xfff80000),
        {0x13,0x48,0x32,0x45,0xca,0x30,0xd6,0xa0,0xae,0xd3,
         0x68,0x38,0x03,0x61,0x73,0x81,0x24,0xd8,0x43,0xf3,0x00}
    },
    {
        /* No.34 delta:7663 weight:713 */
        11213,
        62,
        2,
        15,
        {(0x00000000),
         (0x9fe7bf95),
         (0x7e1ef3d4),
         (0xe1f94c41),
         (0x32e00221),
         (0xad07bdb4),
         (0x4cfef1f5),
         (0xd3194e60),
         (0x00005fc6),
         (0x9fe7e053),
         (0x7e1eac12),
         (0xe1f91387),
         (0x32e05de7),
         (0xad07e272),
         (0x4cfeae33),
         (0xd31911a6)},
        {(0x00000000),
         (0x00000000),
         (0x00000000),
         (0x00000000),
         (0x20000000),
         (0x20000000),
         (0x20000000),
         (0x20000000),
         (0x50001e00),
         (0x50001e00),
         (0x50001e00),
         (0x50001e00),
         (0x70001e00),
         (0x70001e00),
         (0x70001e00),
         (0x70001e00)},
        {(0x3f800000),
         (0x3f800000),
         (0x3f800000),
         (0x3f800000),
         (0x3f900000),
         (0x3f900000),
         (0x3f900000),
         (0x3f900000),
         (0x3fa8000f),
         (0x3fa8000f),
         (0x3fa8000f),
         (0x3fa8000f),
         (0x3fb8000f),
         (0x3fb8000f),
         (0x3fb8000f),
         (0x3fb8000f)},
        (0xfff80000),
        {0x05,0x3c,0xe6,0xed,0x20,0xd0,0x5e,0x82,0xc3,0x47,
         0x89,0x62,0x9a,0x00,0x4f,0xdf,0x8a,0xe2,0x89,0xe4,0x00}
    },
    {
        /* No.35 delta:1045 weight:1387 */
        11213,
        66,
        18,
        8,
        {(0x00000000),
         (0xcc054daa),
         (0x64ce00f1),
         (0xa8cb4d5b),
         (0xc6200238),
         (0x0a254f92),
         (0xa2ee02c9),
         (0x6eeb4f63),
         (0x00006fce),
         (0xcc052264),
         (0x64ce6f3f),
         (0xa8cb2295),
         (0xc6206df6),
         (0x0a25205c),
         (0xa2ee6d07),
         (0x6eeb20ad)},
        {(0x00000000),
         (0xd07d1a00),
         (0x08927800),
         (0xd8ef6200),
         (0x00a4c000),
         (0xd0d9da00),
         (0x0836b800),
         (0xd84ba200),
         (0x00025e00),
         (0xd07f4400),
         (0x08902600),
         (0xd8ed3c00),
         (0x00a69e00),
         (0xd0db8400),
         (0x0834e600),
         (0xd849fc00)},
        {(0x3f800000),
         (0x3fe83e8d),
         (0x3f84493c),
         (0x3fec77b1),
         (0x3f805260),
         (0x3fe86ced),
         (0x3f841b5c),
         (0x3fec25d1),
         (0x3f80012f),
         (0x3fe83fa2),
         (0x3f844813),
         (0x3fec769e),
         (0x3f80534f),
         (0x3fe86dc2),
         (0x3f841a73),
         (0x3fec24fe)},
        (0xfff80000),
        {0x53,0xdb,0x48,0x5e,0x80,0xb8,0xf7,0x7d,0xd4,0x43,
         0xc8,0x11,0x1f,0x88,0x87,0x4f,0x05,0x24,0x64,0x44,0x00}
    },
    {
        /* No.36 delta:2076 weight:1703 */
        11213,
        75,
        9,
        2,
        {(0x00000000),
         (0xc42d5e09),
         (0x3ecbb703),
         (0xfae6e90a),
         (0xe9b00243),
         (0x2d9d5c4a),
         (0xd77bb540),
         (0x1356eb49),
         (0x00003832),
         (0xc42d663b),
         (0x3ecb8f31),
         (0xfae6d138),
         (0xe9b03a71),
         (0x2d9d6478),
         (0xd77b8d72),
         (0x1356d37b)},
        {(0x00000000),
         (0x003c8000),
         (0x00700000),
         (0x004c8000),
         (0x00080000),
         (0x00348000),
         (0x00780000),
         (0x00448000),
         (0x00021e00),
         (0x003e9e00),
         (0x00721e00),
         (0x004e9e00),
         (0x000a1e00),
         (0x00369e00),
         (0x007a1e00),
         (0x00469e00)},
        {(0x3f800000),
         (0x3f801e40),
         (0x3f803800),
         (0x3f802640),
         (0x3f800400),
         (0x3f801a40),
         (0x3f803c00),
         (0x3f802240),
         (0x3f80010f),
         (0x3f801f4f),
         (0x3f80390f),
         (0x3f80274f),
         (0x3f80050f),
         (0x3f801b4f),
         (0x3f803d0f),
         (0x3f80234f)},
        (0xfff80000),
        {0x83,0xeb,0x26,0xe2,0x25,0xfd,0x8b,0x40,0xfc,0xd8,
         0x09,0x63,0xa1,0xb0,0x60,0x56,0xec,0x05,0xc2,0x27,0x00}
    },
    {
        /* No.37 delta:2017 weight:1375 */
        11213,
        20,
        23,
        4,
        {(0x00000000),
         (0xf3dee608),
         (0x43968c18),
         (0xb0486a10),
         (0x82100252),
         (0x71cee45a),
         (0xc1868e4a),
         (0x32586842),
         (0x0000b8e9),
         (0xf3de5ee1),
         (0x439634f1),
         (0xb048d2f9),
         (0x8210babb),
         (0x71ce5cb3),
         (0xc18636a3),
         (0x3258d0ab)},
        {(0x00000000),
         (0x0f350000),
         (0x08b40000),
         (0x07810000),
         (0x20c1a000),
         (0x2ff4a000),
         (0x2875a000),
         (0x2740a000),
         (0x201c1e00),
         (0x2f291e00),
         (0x28a81e00),
         (0x279d1e00),
         (0x00ddbe00),
         (0x0fe8be00),
         (0x0869be00),
         (0x075cbe00)},
        {(0x3f800000),
         (0x3f879a80),
         (0x3f845a00),
         (0x3f83c080),
         (0x3f9060d0),
         (0x3f97fa50),
         (0x3f943ad0),
         (0x3f93a050),
         (0x3f900e0f),
         (0x3f97948f),
         (0x3f94540f),
         (0x3f93ce8f),
         (0x3f806edf),
         (0x3f87f45f),
         (0x3f8434df),
         (0x3f83ae5f)},
        (0xfff80000),
        {0x64,0x1e,0x04,0xb6,0x86,0x5e,0xd5,0x07,0x5c,0xb3,
         0xbe,0x3a,0x1b,0xc8,0xe4,0x45,0x9e,0xc4,0xcf,0x11,0x00}
    },
    {
        /* No.38 delta:2221 weight:1197 */
        11213,
        37,
        24,
        7,
        {(0x00000000),
         (0x4fb7bfce),
         (0x968c3f7b),
         (0xd93b80b5),
         (0x11300267),
         (0x5e87bda9),
         (0x87bc3d1c),
         (0xc80b82d2),
         (0x000002b3),
         (0x4fb7bd7d),
         (0x968c3dc8),
         (0xd93b8206),
         (0x113000d4),
         (0x5e87bf1a),
         (0x87bc3faf),
         (0xc80b8061)},
        {(0x00000000),
         (0x207f2400),
         (0x0138c000),
         (0x2147e400),
         (0x00160000),
         (0x20692400),
         (0x012ec000),
         (0x2151e400),
         (0x30127e00),
         (0x106d5a00),
         (0x312abe00),
         (0x11559a00),
         (0x30047e00),
         (0x107b5a00),
         (0x313cbe00),
         (0x11439a00)},
        {(0x3f800000),
         (0x3f903f92),
         (0x3f809c60),
         (0x3f90a3f2),
         (0x3f800b00),
         (0x3f903492),
         (0x3f809760),
         (0x3f90a8f2),
         (0x3f98093f),
         (0x3f8836ad),
         (0x3f98955f),
         (0x3f88aacd),
         (0x3f98023f),
         (0x3f883dad),
         (0x3f989e5f),
         (0x3f88a1cd)},
        (0xfff80000),
        {0x56,0x6e,0x0e,0xb6,0x39,0x79,0xe8,0x2f,0xdb,0x3a,
         0xb8,0xa6,0xb7,0x15,0xb4,0x26,0x23,0x62,0x6c,0x7b,0x00}
    },
    {
        /* No.39 delta:3255 weight:871 */
        11213,
        59,
        27,
        3,
        {(0x00000000),
         (0xd9be1e48),
         (0x75016ef8),
         (0xacbf70b0),
         (0x0e00027a),
         (0xd7be1c32),
         (0x7b016c82),
         (0xa2bf72ca),
         (0x00004209),
         (0xd9be5c41),
         (0x75012cf1),
         (0xacbf32b9),
         (0x0e004073),
         (0xd7be5e3b),
         (0x7b012e8b),
         (0xa2bf30c3)},
        {(0x00000000),
         (0x14ad8000),
         (0x05013000),
         (0x11acb000),
         (0x092e2000),
         (0x1d83a000),
         (0x0c2f1000),
         (0x18829000),
         (0x07501e00),
         (0x13fd9e00),
         (0x02512e00),
         (0x16fcae00),
         (0x0e7e3e00),
         (0x1ad3be00),
         (0x0b7f0e00),
         (0x1fd28e00)},
        {(0x3f800000),
         (0x3f8a56c0),
         (0x3f828098),
         (0x3f88d658),
         (0x3f849710),
         (0x3f8ec1d0),
         (0x3f861788),
         (0x3f8c4148),
         (0x3f83a80f),
         (0x3f89fecf),
         (0x3f812897),
         (0x3f8b7e57),
         (0x3f873f1f),
         (0x3f8d69df),
         (0x3f85bf87),
         (0x3f8fe947)},
        (0xfff80000),
        {0xde,0x46,0xf8,0xe9,0xab,0x16,0xb9,0x71,0xfd,0x07,
         0x49,0x31,0xbb,0x29,0xb9,0xce,0xd3,0x1e,0x43,0x2e,0x00}
    },
    {
        /* No.40 delta:3532 weight:1325 */
        11213,
        8,
        11,
        4,
        {(0x00000000),
         (0xe314ffbd),
         (0x98f9b31f),
         (0x7bed4ca2),
         (0x71b0028c),
         (0x92a4fd31),
         (0xe949b193),
         (0x0a5d4e2e),
         (0x0000e823),
         (0xe314179e),
         (0x98f95b3c),
         (0x7beda481),
         (0x71b0eaaf),
         (0x92a41512),
         (0xe94959b0),
         (0x0a5da60d)},
        {(0x00000000),
         (0x0c000000),
         (0x0a800000),
         (0x06800000),
         (0x16100000),
         (0x1a100000),
         (0x1c900000),
         (0x10900000),
         (0x95801e00),
         (0x99801e00),
         (0x9f001e00),
         (0x93001e00),
         (0x83901e00),
         (0x8f901e00),
         (0x89101e00),
         (0x85101e00)},
        {(0x3f800000),
         (0x3f860000),
         (0x3f854000),
         (0x3f834000),
         (0x3f8b0800),
         (0x3f8d0800),
         (0x3f8e4800),
         (0x3f884800),
         (0x3fcac00f),
         (0x3fccc00f),
         (0x3fcf800f),
         (0x3fc9800f),
         (0x3fc1c80f),
         (0x3fc7c80f),
         (0x3fc4880f),
         (0x3fc2880f)},
        (0xfff80000),
        {0xc3,0xa5,0xf2,0x8c,0xb3,0x3e,0xd2,0x53,0x92,0x0c,
         0x01,0x4a,0x5c,0x0a,0x47,0xe9,0x85,0xa2,0xf5,0x7f,0x00}
    },
    {
        /* No.41 delta:5222 weight:1281 */
        11213,
        90,
        27,
        2,
        {(0x00000000),
         (0xf1807db4),
         (0x9f84df41),
         (0x6e04a2f5),
         (0x04200296),
         (0xf5a07f22),
         (0x9ba4ddd7),
         (0x6a24a063),
         (0x00005700),
         (0xf1802ab4),
         (0x9f848841),
         (0x6e04f5f5),
         (0x04205596),
         (0xf5a02822),
         (0x9ba48ad7),
         (0x6a24f763)},
        {(0x00000000),
         (0xe9519000),
         (0x1a06a800),
         (0xf3573800),
         (0x0d032000),
         (0xe452b000),
         (0x17058800),
         (0xfe541800),
         (0x036ade00),
         (0xea3b4e00),
         (0x196c7600),
         (0xf03de600),
         (0x0e69fe00),
         (0xe7386e00),
         (0x146f5600),
         (0xfd3ec600)},
        {(0x3f800000),
         (0x3ff4a8c8),
         (0x3f8d0354),
         (0x3ff9ab9c),
         (0x3f868190),
         (0x3ff22958),
         (0x3f8b82c4),
         (0x3fff2a0c),
         (0x3f81b56f),
         (0x3ff51da7),
         (0x3f8cb63b),
         (0x3ff81ef3),
         (0x3f8734ff),
         (0x3ff39c37),
         (0x3f8a37ab),
         (0x3ffe9f63)},
        (0xfff80000),
        {0x08,0x0d,0xba,0x61,0x69,0x6b,0x70,0x44,0xbf,0x27,
         0x4a,0xe9,0xcb,0x98,0x26,0xb9,0xb3,0x5d,0x82,0x3d,0x00}
    },
    {
        /* No.42 delta:1107 weight:1153 */
        11213,
        48,
        16,
        10,
        {(0x00000000),
         (0x6adad7b8),
         (0x649ee9f9),
         (0x0e443e41),
         (0x1b2002a0),
         (0x71fad518),
         (0x7fbeeb59),
         (0x15643ce1),
         (0x0000f400),
         (0x6ada23b8),
         (0x649e1df9),
         (0x0e44ca41),
         (0x1b20f6a0),
         (0x71fa2118),
         (0x7fbe1f59),
         (0x1564c8e1)},
        {(0x00000000),
         (0x09409c00),
         (0x0060c000),
         (0x09205c00),
         (0x04104a00),
         (0x0d50d600),
         (0x04708a00),
         (0x0d301600),
         (0x00087e00),
         (0x0948e200),
         (0x0068be00),
         (0x09282200),
         (0x04183400),
         (0x0d58a800),
         (0x0478f400),
         (0x0d386800)},
        {(0x3f800000),
         (0x3f84a04e),
         (0x3f803060),
         (0x3f84902e),
         (0x3f820825),
         (0x3f86a86b),
         (0x3f823845),
         (0x3f86980b),
         (0x3f80043f),
         (0x3f84a471),
         (0x3f80345f),
         (0x3f849411),
         (0x3f820c1a),
         (0x3f86ac54),
         (0x3f823c7a),
         (0x3f869c34)},
        (0xfff80000),
        {0xb4,0xff,0xa8,0x22,0xa3,0x34,0x7b,0xfd,0xd4,0xca,
         0xef,0x82,0x97,0x4e,0x9d,0x89,0xeb,0xb2,0x55,0x2c,0x00}
    },
    {
        /* No.43 delta:3289 weight:739 */
        11213,
        90,
        3,
        15,
        {(0x00000000),
         (0x12510afd),
         (0xc2094a89),
         (0xd0584074),
         (0x5ce002b7),
         (0x4eb1084a),
         (0x9ee9483e),
         (0x8cb842c3),
         (0x00009dee),
         (0x12519713),
         (0xc209d767),
         (0xd058dd9a),
         (0x5ce09f59),
         (0x4eb195a4),
         (0x9ee9d5d0),
         (0x8cb8df2d)},
        {(0x00000000),
         (0x0c230400),
         (0x033c0000),
         (0x0f1f0400),
         (0x03b08000),
         (0x0f938400),
         (0x008c8000),
         (0x0caf8400),
         (0x08705e00),
         (0x04535a00),
         (0x0b4c5e00),
         (0x076f5a00),
         (0x0bc0de00),
         (0x07e3da00),
         (0x08fcde00),
         (0x04dfda00)},
        {(0x3f800000),
         (0x3f861182),
         (0x3f819e00),
         (0x3f878f82),
         (0x3f81d840),
         (0x3f87c9c2),
         (0x3f804640),
         (0x3f8657c2),
         (0x3f84382f),
         (0x3f8229ad),
         (0x3f85a62f),
         (0x3f83b7ad),
         (0x3f85e06f),
         (0x3f83f1ed),
         (0x3f847e6f),
         (0x3f826fed)},
        (0xfff80000),
        {0x47,0xd8,0xbd,0x66,0xb2,0xe8,0x3c,0x2c,0x45,0x90,
         0x88,0x27,0x9c,0x8f,0x27,0x40,0xc8,0xd9,0x0e,0xd6,0x00}
    },
    {
        /* No.44 delta:1477 weight:1191 */
        11213,
        33,
        20,
        2,
        {(0x00000000),
         (0x3f4d49c2),
         (0xdca457bf),
         (0xe3e91e7d),
         (0x5d5002cf),
         (0x621d4b0d),
         (0x81f45570),
         (0xbeb91cb2),
         (0x0000c277),
         (0x3f4d8bb5),
         (0xdca495c8),
         (0xe3e9dc0a),
         (0x5d50c0b8),
         (0x621d897a),
         (0x81f49707),
         (0xbeb9dec5)},
        {(0x00000000),
         (0x0275b800),
         (0x7149c400),
         (0x733c7c00),
         (0x1058c000),
         (0x122d7800),
         (0x61110400),
         (0x6364bc00),
         (0x006f3e00),
         (0x021a8600),
         (0x7126fa00),
         (0x73534200),
         (0x1037fe00),
         (0x12424600),
         (0x617e3a00),
         (0x630b8200)},
        {(0x3f800000),
         (0x3f813adc),
         (0x3fb8a4e2),
         (0x3fb99e3e),
         (0x3f882c60),
         (0x3f8916bc),
         (0x3fb08882),
         (0x3fb1b25e),
         (0x3f80379f),
         (0x3f810d43),
         (0x3fb8937d),
         (0x3fb9a9a1),
         (0x3f881bff),
         (0x3f892123),
         (0x3fb0bf1d),
         (0x3fb185c1)},
        (0xfff80000),
        {0x43,0xbd,0xc4,0xf1,0xb1,0xd7,0x24,0x47,0xc5,0xd0,
         0xa4,0xbc,0xc9,0x66,0xb3,0x4b,0x26,0xd1,0x26,0x85,0x00}
    },
    {
        /* No.45 delta:1261 weight:1353 */
        11213,
        24,
        18,
        5,
        {(0x00000000),
         (0xabac4677),
         (0x903dfbe7),
         (0x3b91bd90),
         (0xf83002d0),
         (0x539c44a7),
         (0x680df937),
         (0xc3a1bf40),
         (0x00004840),
         (0xabac0e37),
         (0x903db3a7),
         (0x3b91f5d0),
         (0xf8304a90),
         (0x539c0ce7),
         (0x680db177),
         (0xc3a1f700)},
        {(0x00000000),
         (0x00762800),
         (0x200acc00),
         (0x207ce400),
         (0x6ea81000),
         (0x6ede3800),
         (0x4ea2dc00),
         (0x4ed4f400),
         (0x004e1e00),
         (0x00383600),
         (0x2044d200),
         (0x2032fa00),
         (0x6ee60e00),
         (0x6e902600),
         (0x4eecc200),
         (0x4e9aea00)},
        {(0x3f800000),
         (0x3f803b14),
         (0x3f900566),
         (0x3f903e72),
         (0x3fb75408),
         (0x3fb76f1c),
         (0x3fa7516e),
         (0x3fa76a7a),
         (0x3f80270f),
         (0x3f801c1b),
         (0x3f902269),
         (0x3f90197d),
         (0x3fb77307),
         (0x3fb74813),
         (0x3fa77661),
         (0x3fa74d75)},
        (0xfff80000),
        {0x1c,0x79,0xc4,0xaf,0x4a,0xca,0x6f,0x97,0x73,0x87,
         0xe2,0xfa,0x1b,0x02,0x4e,0xfc,0xc2,0xc5,0x85,0xa8,0x00}
    },
    {
        /* No.46 delta:2871 weight:1351 */
        11213,
        24,
        4,
        11,
        {(0x00000000),
         (0x5f7bd2a5),
         (0x9b479801),
         (0xc43c4aa4),
         (0x793002ea),
         (0x264bd04f),
         (0xe2779aeb),
         (0xbd0c484e),
         (0x0000737e),
         (0x5f7ba1db),
         (0x9b47eb7f),
         (0xc43c39da),
         (0x79307194),
         (0x264ba331),
         (0xe277e995),
         (0xbd0c3b30)},
        {(0x00000000),
         (0x0a8c0000),
         (0x10810000),
         (0x1a0d0000),
         (0x00600800),
         (0x0aec0800),
         (0x10e10800),
         (0x1a6d0800),
         (0x40101e00),
         (0x4a9c1e00),
         (0x50911e00),
         (0x5a1d1e00),
         (0x40701600),
         (0x4afc1600),
         (0x50f11600),
         (0x5a7d1600)},
        {(0x3f800000),
         (0x3f854600),
         (0x3f884080),
         (0x3f8d0680),
         (0x3f803004),
         (0x3f857604),
         (0x3f887084),
         (0x3f8d3684),
         (0x3fa0080f),
         (0x3fa54e0f),
         (0x3fa8488f),
         (0x3fad0e8f),
         (0x3fa0380b),
         (0x3fa57e0b),
         (0x3fa8788b),
         (0x3fad3e8b)},
        (0xfff80000),
        {0x23,0xc7,0xec,0x47,0xae,0x9d,0xf4,0x29,0xa3,0xce,
         0x13,0x6c,0x06,0x67,0x5b,0x3a,0xb2,0xac,0x33,0xdb,0x00}
    },
    {
        /* No.47 delta:4370 weight:1245 */
        11213,
        26,
        30,
        4,
        {(0x00000000),
         (0x1af999ae),
         (0x1bfe6053),
         (0x0107f9fd),
         (0xb1f002f1),
         (0xab099b5f),
         (0xaa0e62a2),
         (0xb0f7fb0c),
         (0x0000281b),
         (0x1af9b1b5),
         (0x1bfe4848),
         (0x0107d1e6),
         (0xb1f02aea),
         (0xab09b344),
         (0xaa0e4ab9),
         (0xb0f7d317)},
        {(0x00000000),
         (0x0c201000),
         (0x03140000),
         (0x0f341000),
         (0xe4200800),
         (0xe8001800),
         (0xe7340800),
         (0xeb141800),
         (0x08001e00),
         (0x04200e00),
         (0x0b141e00),
         (0x07340e00),
         (0xec201600),
         (0xe0000600),
         (0xef341600),
         (0xe3140600)},
        {(0x3f800000),
         (0x3f861008),
         (0x3f818a00),
         (0x3f879a08),
         (0x3ff21004),
         (0x3ff4000c),
         (0x3ff39a04),
         (0x3ff58a0c),
         (0x3f84000f),
         (0x3f821007),
         (0x3f858a0f),
         (0x3f839a07),
         (0x3ff6100b),
         (0x3ff00003),
         (0x3ff79a0b),
         (0x3ff18a03)},
        (0xfff80000),
        {0x98,0x58,0x06,0x9e,0x81,0x14,0x32,0xd7,0x93,0x32,
         0x65,0xc8,0x13,0x43,0x02,0x53,0x1e,0xd3,0xaf,0x94,0x00}
    },
    {
        /* No.48 delta:1177 weight:859 */
        11213,
        75,
        13,
        16,
        {(0x00000000),
         (0x8e9ecf83),
         (0xb438b3ee),
         (0x3aa67c6d),
         (0xbd800305),
         (0x331ecc86),
         (0x09b8b0eb),
         (0x87267f68),
         (0x0000d641),
         (0x8e9e19c2),
         (0xb43865af),
         (0x3aa6aa2c),
         (0xbd80d544),
         (0x331e1ac7),
         (0x09b866aa),
         (0x8726a929)},
        {(0x00000000),
         (0x0f7d1c00),
         (0x76262000),
         (0x795b3c00),
         (0x41532000),
         (0x4e2e3c00),
         (0x37750000),
         (0x38081c00),
         (0x00429e00),
         (0x0f3f8200),
         (0x7664be00),
         (0x7919a200),
         (0x4111be00),
         (0x4e6ca200),
         (0x37379e00),
         (0x384a8200)},
        {(0x3f800000),
         (0x3f87be8e),
         (0x3fbb1310),
         (0x3fbcad9e),
         (0x3fa0a990),
         (0x3fa7171e),
         (0x3f9bba80),
         (0x3f9c040e),
         (0x3f80214f),
         (0x3f879fc1),
         (0x3fbb325f),
         (0x3fbc8cd1),
         (0x3fa088df),
         (0x3fa73651),
         (0x3f9b9bcf),
         (0x3f9c2541)},
        (0xfff80000),
        {0xc9,0xe0,0x77,0x04,0x4f,0x92,0x96,0xd4,0x05,0xdc,
         0x3e,0x3c,0xa6,0xf3,0x8e,0x2d,0xdb,0xf2,0x8c,0xe4,0x00}
    },
    {
        /* No.49 delta:3224 weight:947 */
        11213,
        65,
        7,
        13,
        {(0x00000000),
         (0x9221eadf),
         (0x5003b0d0),
         (0xc2225a0f),
         (0x0d800317),
         (0x9fa1e9c8),
         (0x5d83b3c7),
         (0xcfa25918),
         (0x00005031),
         (0x9221baee),
         (0x5003e0e1),
         (0xc2220a3e),
         (0x0d805326),
         (0x9fa1b9f9),
         (0x5d83e3f6),
         (0xcfa20929)},
        {(0x00000000),
         (0x00400800),
         (0x60600000),
         (0x60200800),
         (0x00000000),
         (0x00400800),
         (0x60600000),
         (0x60200800),
         (0x30101e00),
         (0x30501600),
         (0x50701e00),
         (0x50301600),
         (0x30101e00),
         (0x30501600),
         (0x50701e00),
         (0x50301600)},
        {(0x3f800000),
         (0x3f802004),
         (0x3fb03000),
         (0x3fb01004),
         (0x3f800000),
         (0x3f802004),
         (0x3fb03000),
         (0x3fb01004),
         (0x3f98080f),
         (0x3f98280b),
         (0x3fa8380f),
         (0x3fa8180b),
         (0x3f98080f),
         (0x3f98280b),
         (0x3fa8380f),
         (0x3fa8180b)},
        (0xfff80000),
        {0xc1,0x70,0x72,0x7f,0xb1,0x1e,0x6e,0xc5,0xec,0xcc,
         0x69,0xd8,0x0b,0xba,0x4e,0x2c,0x73,0x46,0x81,0x52,0x00}
    },
    {
        /* No.50 delta:6665 weight:1221 */
        11213,
        84,
        1,
        9,
        {(0x00000000),
         (0xbdb9b41e),
         (0x9876c465),
         (0x25cf707b),
         (0x25c0032f),
         (0x9879b731),
         (0xbdb6c74a),
         (0x000f7354),
         (0x00003009),
         (0xbdb98417),
         (0x9876f46c),
         (0x25cf4072),
         (0x25c03326),
         (0x98798738),
         (0xbdb6f743),
         (0x000f435d)},
        {(0x00000000),
         (0x51000000),
         (0x20000000),
         (0x71000000),
         (0x04200000),
         (0x55200000),
         (0x24200000),
         (0x75200000),
         (0x17201e00),
         (0x46201e00),
         (0x37201e00),
         (0x66201e00),
         (0x13001e00),
         (0x42001e00),
         (0x33001e00),
         (0x62001e00)},
        {(0x3f800000),
         (0x3fa88000),
         (0x3f900000),
         (0x3fb88000),
         (0x3f821000),
         (0x3faa9000),
         (0x3f921000),
         (0x3fba9000),
         (0x3f8b900f),
         (0x3fa3100f),
         (0x3f9b900f),
         (0x3fb3100f),
         (0x3f89800f),
         (0x3fa1000f),
         (0x3f99800f),
         (0x3fb1000f)},
        (0xfff80000),
        {0xdf,0xd6,0xd2,0xb4,0x0a,0xa4,0x35,0xbe,0x0f,0xa6,
         0x48,0x42,0xfe,0x0c,0x0f,0xa3,0xa9,0x64,0x1b,0x93,0x00}
    },
    {
        /* No.51 delta:6604 weight:1075 */
        11213,
        61,
        3,
        13,
        {(0x00000000),
         (0xe7ec9698),
         (0xdc943f6e),
         (0x3b78a9f6),
         (0xdf600330),
         (0x388c95a8),
         (0x03f43c5e),
         (0xe418aac6),
         (0x000058c3),
         (0xe7ecce5b),
         (0xdc9467ad),
         (0x3b78f135),
         (0xdf605bf3),
         (0x388ccd6b),
         (0x03f4649d),
         (0xe418f205)},
        {(0x00000000),
         (0x80000000),
         (0x50000000),
         (0xd0000000),
         (0x20000000),
         (0xa0000000),
         (0x70000000),
         (0xf0000000),
         (0x00001e00),
         (0x80001e00),
         (0x50001e00),
         (0xd0001e00),
         (0x20001e00),
         (0xa0001e00),
         (0x70001e00),
         (0xf0001e00)},
        {(0x3f800000),
         (0x3fc00000),
         (0x3fa80000),
         (0x3fe80000),
         (0x3f900000),
         (0x3fd00000),
         (0x3fb80000),
         (0x3ff80000),
         (0x3f80000f),
         (0x3fc0000f),
         (0x3fa8000f),
         (0x3fe8000f),
         (0x3f90000f),
         (0x3fd0000f),
         (0x3fb8000f),
         (0x3ff8000f)},
        (0xfff80000),
        {0xf2,0x95,0xcc,0x96,0x13,0xa0,0x75,0xc2,0xea,0x1a,
         0x5c,0x2d,0xcc,0x41,0x22,0xf8,0x87,0x8d,0x52,0xc0,0x00}
    },
    {
        /* No.52 delta:980 weight:1115 */
        11213,
        75,
        17,
        12,
        {(0x00000000),
         (0x619af787),
         (0x463e8326),
         (0x27a474a1),
         (0xc5c00348),
         (0xa45af4cf),
         (0x83fe806e),
         (0xe26477e9),
         (0x00008ae3),
         (0x619a7d64),
         (0x463e09c5),
         (0x27a4fe42),
         (0xc5c089ab),
         (0xa45a7e2c),
         (0x83fe0a8d),
         (0xe264fd0a)},
        {(0x00000000),
         (0x107c1400),
         (0x81204400),
         (0x915c5000),
         (0x00113400),
         (0x106d2000),
         (0x81317000),
         (0x914d6400),
         (0x000ede00),
         (0x1072ca00),
         (0x812e9a00),
         (0x91528e00),
         (0x001fea00),
         (0x1063fe00),
         (0x813fae00),
         (0x9143ba00)},
        {(0x3f800000),
         (0x3f883e0a),
         (0x3fc09022),
         (0x3fc8ae28),
         (0x3f80089a),
         (0x3f883690),
         (0x3fc098b8),
         (0x3fc8a6b2),
         (0x3f80076f),
         (0x3f883965),
         (0x3fc0974d),
         (0x3fc8a947),
         (0x3f800ff5),
         (0x3f8831ff),
         (0x3fc09fd7),
         (0x3fc8a1dd)},
        (0xfff80000),
        {0x31,0x3b,0x59,0xba,0x4b,0xfa,0x15,0x6d,0x68,0x38,
         0xd0,0x59,0x4b,0xc5,0xee,0xd6,0xd1,0x72,0x77,0x56,0x00}
    },
    {
        /* No.53 delta:2128 weight:1031 */
        11213,
        90,
        18,
        10,
        {(0x00000000),
         (0x54ddf61b),
         (0x3289e26b),
         (0x66541470),
         (0x8c500359),
         (0xd88df542),
         (0xbed9e132),
         (0xea041729),
         (0x0000fb30),
         (0x54dd0d2b),
         (0x3289195b),
         (0x6654ef40),
         (0x8c50f869),
         (0xd88d0e72),
         (0xbed91a02),
         (0xea04ec19)},
        {(0x00000000),
         (0x80710000),
         (0x704e0000),
         (0xf03f0000),
         (0x40450000),
         (0xc0340000),
         (0x300b0000),
         (0xb07a0000),
         (0x000c1e00),
         (0x807d1e00),
         (0x70421e00),
         (0xf0331e00),
         (0x40491e00),
         (0xc0381e00),
         (0x30071e00),
         (0xb0761e00)},
        {(0x3f800000),
         (0x3fc03880),
         (0x3fb82700),
         (0x3ff81f80),
         (0x3fa02280),
         (0x3fe01a00),
         (0x3f980580),
         (0x3fd83d00),
         (0x3f80060f),
         (0x3fc03e8f),
         (0x3fb8210f),
         (0x3ff8198f),
         (0x3fa0248f),
         (0x3fe01c0f),
         (0x3f98038f),
         (0x3fd83b0f)},
        (0xfff80000),
        {0x2c,0x15,0xc7,0x91,0x8f,0xe7,0xba,0x43,0x56,0x9b,
         0xf8,0xd4,0x59,0xf2,0x0c,0x4e,0xf2,0x42,0x9a,0xfe,0x00}
    },
    {
        /* No.54 delta:2047 weight:935 */
        11213,
        73,
        6,
        15,
        {(0x00000000),
         (0x1b3e4d84),
         (0x40583cec),
         (0x5b667168),
         (0x82600363),
         (0x995e4ee7),
         (0xc2383f8f),
         (0xd906720b),
         (0x000025e9),
         (0x1b3e686d),
         (0x40581905),
         (0x5b665481),
         (0x8260268a),
         (0x995e6b0e),
         (0xc2381a66),
         (0xd90657e2)},
        {(0x00000000),
         (0x00524000),
         (0x52804000),
         (0x52d20000),
         (0x00232000),
         (0x00716000),
         (0x52a36000),
         (0x52f12000),
         (0x21c51e00),
         (0x21975e00),
         (0x73455e00),
         (0x73171e00),
         (0x21e63e00),
         (0x21b47e00),
         (0x73667e00),
         (0x73343e00)},
        {(0x3f800000),
         (0x3f802920),
         (0x3fa94020),
         (0x3fa96900),
         (0x3f801190),
         (0x3f8038b0),
         (0x3fa951b0),
         (0x3fa97890),
         (0x3f90e28f),
         (0x3f90cbaf),
         (0x3fb9a2af),
         (0x3fb98b8f),
         (0x3f90f31f),
         (0x3f90da3f),
         (0x3fb9b33f),
         (0x3fb99a1f)},
        (0xfff80000),
        {0x67,0xd5,0xbe,0x19,0x7f,0x4e,0x37,0xca,0xeb,0x0b,
         0x09,0x52,0xc2,0x91,0x23,0x02,0xb9,0xa6,0xdd,0x1e,0x00}
    },
    {
        /* No.55 delta:1367 weight:1089 */
        11213,
        30,
        14,
        10,
        {(0x00000000),
         (0x3d4e7360),
         (0xd47d559c),
         (0xe93326fc),
         (0x10e00371),
         (0x2dae7011),
         (0xc49d56ed),
         (0xf9d3258d),
         (0x0000f71b),
         (0x3d4e847b),
         (0xd47da287),
         (0xe933d1e7),
         (0x10e0f46a),
         (0x2dae870a),
         (0xc49da1f6),
         (0xf9d3d296)},
        {(0x00000000),
         (0x6b729000),
         (0x206f1800),
         (0x4b1d8800),
         (0x00501a00),
         (0x6b228a00),
         (0x203f0200),
         (0x4b4d9200),
         (0x20103e00),
         (0x4b62ae00),
         (0x007f2600),
         (0x6b0db600),
         (0x20402400),
         (0x4b32b400),
         (0x002f3c00),
         (0x6b5dac00)},
        {(0x3f800000),
         (0x3fb5b948),
         (0x3f90378c),
         (0x3fa58ec4),
         (0x3f80280d),
         (0x3fb59145),
         (0x3f901f81),
         (0x3fa5a6c9),
         (0x3f90081f),
         (0x3fa5b157),
         (0x3f803f93),
         (0x3fb586db),
         (0x3f902012),
         (0x3fa5995a),
         (0x3f80179e),
         (0x3fb5aed6)},
        (0xfff80000),
        {0x74,0x06,0xc4,0x94,0x50,0xc8,0x89,0xd9,0x6c,0x24,
         0x1b,0x4e,0x7f,0x96,0xbe,0x0a,0xb0,0xbb,0xa9,0x75,0x00}
    },
    {
        /* No.56 delta:1232 weight:1251 */
        11213,
        56,
        20,
        8,
        {(0x00000000),
         (0x303bfd65),
         (0x96ea74b0),
         (0xa6d189d5),
         (0x1d200385),
         (0x2d1bfee0),
         (0x8bca7735),
         (0xbbf18a50),
         (0x0000e0a3),
         (0x303b1dc6),
         (0x96ea9413),
         (0xa6d16976),
         (0x1d20e326),
         (0x2d1b1e43),
         (0x8bca9796),
         (0xbbf16af3)},
        {(0x00000000),
         (0x0e9c1400),
         (0x00661e00),
         (0x0efa0a00),
         (0x20125000),
         (0x2e8e4400),
         (0x20744e00),
         (0x2ee85a00),
         (0x001abe00),
         (0x0e86aa00),
         (0x007ca000),
         (0x0ee0b400),
         (0x2008ee00),
         (0x2e94fa00),
         (0x206ef000),
         (0x2ef2e400)},
        {(0x3f800000),
         (0x3f874e0a),
         (0x3f80330f),
         (0x3f877d05),
         (0x3f900928),
         (0x3f974722),
         (0x3f903a27),
         (0x3f97742d),
         (0x3f800d5f),
         (0x3f874355),
         (0x3f803e50),
         (0x3f87705a),
         (0x3f900477),
         (0x3f974a7d),
         (0x3f903778),
         (0x3f977972)},
        (0xfff80000),
        {0x9f,0xb8,0x5c,0x52,0x53,0x38,0x40,0xed,0x9b,0x43,
         0xe8,0x89,0x8c,0xd0,0x44,0x25,0x4b,0xbf,0xca,0xd0,0x00}
    },
    {
        /* No.57 delta:1379 weight:873 */
        11213,
        89,
        13,
        1,
        {(0x00000000),
         (0xfacfdd0b),
         (0x22ecafbf),
         (0xd82372b4),
         (0x85500390),
         (0x7f9fde9b),
         (0xa7bcac2f),
         (0x5d737124),
         (0x0000d423),
         (0xfacf0928),
         (0x22ec7b9c),
         (0xd823a697),
         (0x8550d7b3),
         (0x7f9f0ab8),
         (0xa7bc780c),
         (0x5d73a507)},
        {(0x00000000),
         (0x2c081000),
         (0x05201800),
         (0x29280800),
         (0x20e00400),
         (0x0ce81400),
         (0x25c01c00),
         (0x09c80c00),
         (0x11481e00),
         (0x3d400e00),
         (0x14680600),
         (0x38601600),
         (0x31a81a00),
         (0x1da00a00),
         (0x34880200),
         (0x18801200)},
        {(0x3f800000),
         (0x3f960408),
         (0x3f82900c),
         (0x3f949404),
         (0x3f907002),
         (0x3f86740a),
         (0x3f92e00e),
         (0x3f84e406),
         (0x3f88a40f),
         (0x3f9ea007),
         (0x3f8a3403),
         (0x3f9c300b),
         (0x3f98d40d),
         (0x3f8ed005),
         (0x3f9a4401),
         (0x3f8c4009)},
        (0xfff80000),
        {0x08,0x0f,0x24,0x9d,0x3a,0xba,0x6d,0xb4,0xb0,0x1a,
         0x1c,0x4b,0x44,0xe7,0x44,0xf7,0xc9,0x4a,0x5f,0x97,0x00}
    },
    {
        /* No.58 delta:904 weight:1097 */
        11213,
        60,
        16,
        2,
        {(0x00000000),
         (0xda86476d),
         (0x6c05798d),
         (0xb6833ee0),
         (0xb72003ae),
         (0x6da644c3),
         (0xdb257a23),
         (0x01a33d4e),
         (0x00001489),
         (0xda8653e4),
         (0x6c056d04),
         (0xb6832a69),
         (0xb7201727),
         (0x6da6504a),
         (0xdb256eaa),
         (0x01a329c7)},
        {(0x00000000),
         (0x0c74f000),
         (0x0059a800),
         (0x0c2d5800),
         (0x02445000),
         (0x0e30a000),
         (0x021df800),
         (0x0e690800),
         (0x0902de00),
         (0x05762e00),
         (0x095b7600),
         (0x052f8600),
         (0x0b468e00),
         (0x07327e00),
         (0x0b1f2600),
         (0x076bd600)},
        {(0x3f800000),
         (0x3f863a78),
         (0x3f802cd4),
         (0x3f8616ac),
         (0x3f812228),
         (0x3f871850),
         (0x3f810efc),
         (0x3f873484),
         (0x3f84816f),
         (0x3f82bb17),
         (0x3f84adbb),
         (0x3f8297c3),
         (0x3f85a347),
         (0x3f83993f),
         (0x3f858f93),
         (0x3f83b5eb)},
        (0xfff80000),
        {0xb8,0xe0,0xa0,0x3b,0x2e,0x11,0x17,0x83,0x7b,0x75,
         0x85,0x89,0xfa,0x1e,0x49,0xb4,0x5c,0xe6,0xb8,0xc1,0x00}
    },
    {
        /* No.59 delta:7724 weight:1539 */
        11213,
        42,
        28,
        4,
        {(0x00000000),
         (0xdfefef22),
         (0x804185a4),
         (0x5fae6a86),
         (0x54b003b3),
         (0x8b5fec91),
         (0xd4f18617),
         (0x0b1e6935),
         (0x00009211),
         (0xdfef7d33),
         (0x804117b5),
         (0x5faef897),
         (0x54b091a2),
         (0x8b5f7e80),
         (0xd4f11406),
         (0x0b1efb24)},
        {(0x00000000),
         (0x38020000),
         (0x08014000),
         (0x30034000),
         (0x04000800),
         (0x3c020800),
         (0x0c014800),
         (0x34034800),
         (0x08001e00),
         (0x30021e00),
         (0x00015e00),
         (0x38035e00),
         (0x0c001600),
         (0x34021600),
         (0x04015600),
         (0x3c035600)},
        {(0x3f800000),
         (0x3f9c0100),
         (0x3f8400a0),
         (0x3f9801a0),
         (0x3f820004),
         (0x3f9e0104),
         (0x3f8600a4),
         (0x3f9a01a4),
         (0x3f84000f),
         (0x3f98010f),
         (0x3f8000af),
         (0x3f9c01af),
         (0x3f86000b),
         (0x3f9a010b),
         (0x3f8200ab),
         (0x3f9e01ab)},
        (0xfff80000),
        {0x91,0xba,0x8c,0xac,0x44,0xa7,0x81,0xa7,0xed,0x37,
         0x7e,0x8d,0xaf,0xe5,0x08,0xe6,0xc7,0x1d,0xfd,0xe5,0x00}
    },
    {
        /* No.60 delta:2411 weight:1317 */
        11213,
        13,
        24,
        5,
        {(0x00000000),
         (0x9dd3ce8f),
         (0x90da0878),
         (0x0d09c6f7),
         (0xad8003cf),
         (0x3053cd40),
         (0x3d5a0bb7),
         (0xa089c538),
         (0x0000bcb0),
         (0x9dd3723f),
         (0x90dab4c8),
         (0x0d097a47),
         (0xad80bf7f),
         (0x305371f0),
         (0x3d5ab707),
         (0xa0897988)},
        {(0x00000000),
         (0x4c0b9800),
         (0x0fa54000),
         (0x43aed800),
         (0x0067c000),
         (0x4c6c5800),
         (0x0fc28000),
         (0x43c91800),
         (0x0a105e00),
         (0x461bc600),
         (0x05b51e00),
         (0x49be8600),
         (0x0a779e00),
         (0x467c0600),
         (0x05d2de00),
         (0x49d94600)},
        {(0x3f800000),
         (0x3fa605cc),
         (0x3f87d2a0),
         (0x3fa1d76c),
         (0x3f8033e0),
         (0x3fa6362c),
         (0x3f87e140),
         (0x3fa1e48c),
         (0x3f85082f),
         (0x3fa30de3),
         (0x3f82da8f),
         (0x3fa4df43),
         (0x3f853bcf),
         (0x3fa33e03),
         (0x3f82e96f),
         (0x3fa4eca3)},
        (0xfff80000),
        {0xac,0x0b,0xb8,0x4d,0x6f,0xbd,0x47,0x8b,0x3f,0x9c,
         0x92,0xd0,0x32,0xf9,0x89,0x38,0xda,0x67,0xea,0xbe,0x00}
    },
    {
        /* No.61 delta:1265 weight:827 */
        11213,
        52,
        17,
        15,
        {(0x00000000),
         (0xa4b47608),
         (0x36bc0fca),
         (0x920879c2),
         (0xdeb003d6),
         (0x7a0475de),
         (0xe80c0c1c),
         (0x4cb87a14),
         (0x0000fbcf),
         (0xa4b48dc7),
         (0x36bcf405),
         (0x9208820d),
         (0xdeb0f819),
         (0x7a048e11),
         (0xe80cf7d3),
         (0x4cb881db)},
        {(0x00000000),
         (0x0813ae00),
         (0x20151000),
         (0x2806be00),
         (0x4029c000),
         (0x483a6e00),
         (0x603cd000),
         (0x682f7e00),
         (0x52035e00),
         (0x5a10f000),
         (0x72164e00),
         (0x7a05e000),
         (0x122a9e00),
         (0x1a393000),
         (0x323f8e00),
         (0x3a2c2000)},
        {(0x3f800000),
         (0x3f8409d7),
         (0x3f900a88),
         (0x3f94035f),
         (0x3fa014e0),
         (0x3fa41d37),
         (0x3fb01e68),
         (0x3fb417bf),
         (0x3fa901af),
         (0x3fad0878),
         (0x3fb90b27),
         (0x3fbd02f0),
         (0x3f89154f),
         (0x3f8d1c98),
         (0x3f991fc7),
         (0x3f9d1610)},
        (0xfff80000),
        {0xb2,0x25,0x08,0xf6,0xcd,0xda,0xfa,0xdb,0x2f,0xe7,
         0x81,0x83,0x7f,0xb8,0x75,0x05,0xff,0x2c,0x1a,0x80,0x00}
    },
    {
        /* No.62 delta:3397 weight:1223 */
        11213,
        44,
        3,
        7,
        {(0x00000000),
         (0x89ffcb3c),
         (0xafe67560),
         (0x2619be5c),
         (0xf79003e8),
         (0x7e6fc8d4),
         (0x58767688),
         (0xd189bdb4),
         (0x0000dcdc),
         (0x89ff17e0),
         (0xafe6a9bc),
         (0x26196280),
         (0xf790df34),
         (0x7e6f1408),
         (0x5876aa54),
         (0xd1896168)},
        {(0x00000000),
         (0x00710000),
         (0xb00e0800),
         (0xb07f0800),
         (0x90120000),
         (0x90630000),
         (0x201c0800),
         (0x206d0800),
         (0x40091e00),
         (0x40781e00),
         (0xf0071600),
         (0xf0761600),
         (0xd01b1e00),
         (0xd06a1e00),
         (0x60151600),
         (0x60641600)},
        {(0x3f800000),
         (0x3f803880),
         (0x3fd80704),
         (0x3fd83f84),
         (0x3fc80900),
         (0x3fc83180),
         (0x3f900e04),
         (0x3f903684),
         (0x3fa0048f),
         (0x3fa03c0f),
         (0x3ff8038b),
         (0x3ff83b0b),
         (0x3fe80d8f),
         (0x3fe8350f),
         (0x3fb00a8b),
         (0x3fb0320b)},
        (0xfff80000),
        {0x65,0x92,0xc9,0x30,0xd4,0x39,0xe2,0xf8,0x3a,0x06,
         0x72,0x5a,0xf0,0xe9,0x6f,0x1d,0x41,0xb8,0x38,0xcb,0x00}
    },
    {
        /* No.63 delta:1253 weight:1063 */
        11213,
        40,
        15,
        8,
        {(0x00000000),
         (0xf0f1fa2b),
         (0xc095cbf9),
         (0x306431d2),
         (0x84c003f6),
         (0x7431f9dd),
         (0x4455c80f),
         (0xb4a43224),
         (0x000041b7),
         (0xf0f1bb9c),
         (0xc0958a4e),
         (0x30647065),
         (0x84c04241),
         (0x7431b86a),
         (0x445589b8),
         (0xb4a47393)},
        {(0x00000000),
         (0x000f4800),
         (0x00c47000),
         (0x00cb3800),
         (0x001ec000),
         (0x00118800),
         (0x00dab000),
         (0x00d5f800),
         (0xc01d9e00),
         (0xc012d600),
         (0xc0d9ee00),
         (0xc0d6a600),
         (0xc0035e00),
         (0xc00c1600),
         (0xc0c72e00),
         (0xc0c86600)},
        {(0x3f800000),
         (0x3f8007a4),
         (0x3f806238),
         (0x3f80659c),
         (0x3f800f60),
         (0x3f8008c4),
         (0x3f806d58),
         (0x3f806afc),
         (0x3fe00ecf),
         (0x3fe0096b),
         (0x3fe06cf7),
         (0x3fe06b53),
         (0x3fe001af),
         (0x3fe0060b),
         (0x3fe06397),
         (0x3fe06433)},
        (0xfff80000),
        {0x6a,0x42,0x24,0x72,0x49,0x2e,0x6c,0xc6,0x39,0xde,
         0x29,0x45,0x5e,0xba,0x3b,0xda,0xc6,0xb7,0x79,0xd2,0x00}
    },
    {
        /* No.64 delta:5028 weight:833 */
        11213,
        40,
        3,
        16,
        {(0x00000000),
         (0x0fda9709),
         (0xdb5d90d9),
         (0xd48707d0),
         (0x78500401),
         (0x778a9308),
         (0xa30d94d8),
         (0xacd703d1),
         (0x00000aa4),
         (0x0fda9dad),
         (0xdb5d9a7d),
         (0xd4870d74),
         (0x78500ea5),
         (0x778a99ac),
         (0xa30d9e7c),
         (0xacd70975)},
        {(0x00000000),
         (0x19200000),
         (0x83a00000),
         (0x9a800000),
         (0x00800000),
         (0x19a00000),
         (0x83200000),
         (0x9a000000),
         (0x41001e00),
         (0x58201e00),
         (0xc2a01e00),
         (0xdb801e00),
         (0x41801e00),
         (0x58a01e00),
         (0xc2201e00),
         (0xdb001e00)},
        {(0x3f800000),
         (0x3f8c9000),
         (0x3fc1d000),
         (0x3fcd4000),
         (0x3f804000),
         (0x3f8cd000),
         (0x3fc19000),
         (0x3fcd0000),
         (0x3fa0800f),
         (0x3fac100f),
         (0x3fe1500f),
         (0x3fedc00f),
         (0x3fa0c00f),
         (0x3fac500f),
         (0x3fe1100f),
         (0x3fed800f)},
        (0xfff80000),
        {0x7f,0xa7,0x79,0xc1,0x02,0x5a,0xb9,0x48,0xd7,0xc2,
         0x1b,0xde,0x64,0x28,0xca,0x0c,0xa1,0xb8,0xbd,0x71,0x00}
    },
    {
        /* No.65 delta:1199 weight:1181 */
        11213,
        35,
        20,
        9,
        {(0x00000000),
         (0xf97f5fb7),
         (0x820665df),
         (0x7b793a68),
         (0xeda00417),
         (0x14df5ba0),
         (0x6fa661c8),
         (0x96d93e7f),
         (0x0000be8a),
         (0xf97fe13d),
         (0x8206db55),
         (0x7b7984e2),
         (0xeda0ba9d),
         (0x14dfe52a),
         (0x6fa6df42),
         (0x96d980f5)},
        {(0x00000000),
         (0x0c163000),
         (0x000c1000),
         (0x0c1a2000),
         (0xc0028000),
         (0xcc14b000),
         (0xc00e9000),
         (0xcc18a000),
         (0x7101de00),
         (0x7d17ee00),
         (0x710dce00),
         (0x7d1bfe00),
         (0xb1035e00),
         (0xbd156e00),
         (0xb10f4e00),
         (0xbd197e00)},
        {(0x3f800000),
         (0x3f860b18),
         (0x3f800608),
         (0x3f860d10),
         (0x3fe00140),
         (0x3fe60a58),
         (0x3fe00748),
         (0x3fe60c50),
         (0x3fb880ef),
         (0x3fbe8bf7),
         (0x3fb886e7),
         (0x3fbe8dff),
         (0x3fd881af),
         (0x3fde8ab7),
         (0x3fd887a7),
         (0x3fde8cbf)},
        (0xfff80000),
        {0x44,0x09,0x7e,0x16,0x6a,0x0b,0xbf,0xd6,0xf6,0x83,
         0xc1,0x33,0x24,0x8d,0x24,0x40,0xc5,0xee,0xb0,0xd2,0x00}
    },
    {
        /* No.66 delta:1553 weight:539 */
        11213,
        26,
        13,
        19,
        {(0x00000000),
         (0xc75eb6dc),
         (0xfa4bc661),
         (0x3d1570bd),
         (0x6f600428),
         (0xa83eb2f4),
         (0x952bc249),
         (0x52757495),
         (0x0000df90),
         (0xc75e694c),
         (0xfa4b19f1),
         (0x3d15af2d),
         (0x6f60dbb8),
         (0xa83e6d64),
         (0x952b1dd9),
         (0x5275ab05)},
        {(0x00000000),
         (0x00521800),
         (0x20647400),
         (0x20366c00),
         (0x60dc1800),
         (0x608e0000),
         (0x40b86c00),
         (0x40ea7400),
         (0x10341e00),
         (0x10660600),
         (0x30506a00),
         (0x30027200),
         (0x70e80600),
         (0x70ba1e00),
         (0x508c7200),
         (0x50de6a00)},
        {(0x3f800000),
         (0x3f80290c),
         (0x3f90323a),
         (0x3f901b36),
         (0x3fb06e0c),
         (0x3fb04700),
         (0x3fa05c36),
         (0x3fa0753a),
         (0x3f881a0f),
         (0x3f883303),
         (0x3f982835),
         (0x3f980139),
         (0x3fb87403),
         (0x3fb85d0f),
         (0x3fa84639),
         (0x3fa86f35)},
        (0xfff80000),
        {0xe3,0x81,0xfe,0x0e,0xae,0xa6,0x17,0x99,0xb5,0xdc,
         0x36,0xa3,0xa9,0x84,0x5e,0xac,0xaf,0x0b,0xa6,0x5e,0x00}
    },
    {
        /* No.67 delta:1370 weight:1349 */
        11213,
        50,
        8,
        4,
        {(0x00000000),
         (0xf2eee92e),
         (0x184a48a6),
         (0xeaa4a188),
         (0x3cb00439),
         (0xce5eed17),
         (0x24fa4c9f),
         (0xd614a5b1),
         (0x0000c236),
         (0xf2ee2b18),
         (0x184a8a90),
         (0xeaa463be),
         (0x3cb0c60f),
         (0xce5e2f21),
         (0x24fa8ea9),
         (0xd6146787)},
        {(0x00000000),
         (0x40028000),
         (0x70014000),
         (0x3003c000),
         (0x20002000),
         (0x6002a000),
         (0x50016000),
         (0x1003e000),
         (0x10001e00),
         (0x50029e00),
         (0x60015e00),
         (0x2003de00),
         (0x30003e00),
         (0x7002be00),
         (0x40017e00),
         (0x0003fe00)},
        {(0x3f800000),
         (0x3fa00140),
         (0x3fb800a0),
         (0x3f9801e0),
         (0x3f900010),
         (0x3fb00150),
         (0x3fa800b0),
         (0x3f8801f0),
         (0x3f88000f),
         (0x3fa8014f),
         (0x3fb000af),
         (0x3f9001ef),
         (0x3f98001f),
         (0x3fb8015f),
         (0x3fa000bf),
         (0x3f8001ff)},
        (0xfff80000),
        {0xce,0xab,0xcf,0x53,0x4e,0xc1,0xc1,0xd0,0x64,0x84,
         0xd7,0xc3,0xad,0x13,0xad,0xf4,0xf7,0x14,0xe1,0xd6,0x00}
    },
    {
        /* No.68 delta:1769 weight:1053 */
        11213,
        44,
        23,
        7,
        {(0x00000000),
         (0x1c10f784),
         (0xb81f8711),
         (0xa40f7095),
         (0xd0d00449),
         (0xccc0f3cd),
         (0x68cf8358),
         (0x74df74dc),
         (0x00006582),
         (0x1c109206),
         (0xb81fe293),
         (0xa40f1517),
         (0xd0d061cb),
         (0xccc0964f),
         (0x68cfe6da),
         (0x74df115e)},
        {(0x00000000),
         (0x009da000),
         (0x40746000),
         (0x40e9c000),
         (0x00028400),
         (0x009f2400),
         (0x4076e400),
         (0x40eb4400),
         (0x700bbe00),
         (0x70961e00),
         (0x307fde00),
         (0x30e27e00),
         (0x70093a00),
         (0x70949a00),
         (0x307d5a00),
         (0x30e0fa00)},
        {(0x3f800000),
         (0x3f804ed0),
         (0x3fa03a30),
         (0x3fa074e0),
         (0x3f800142),
         (0x3f804f92),
         (0x3fa03b72),
         (0x3fa075a2),
         (0x3fb805df),
         (0x3fb84b0f),
         (0x3f983fef),
         (0x3f98713f),
         (0x3fb8049d),
         (0x3fb84a4d),
         (0x3f983ead),
         (0x3f98707d)},
        (0xfff80000),
        {0x25,0xc5,0x2b,0x7e,0x94,0x70,0x50,0xab,0x68,0x79,
         0xe0,0x34,0x49,0x1e,0xd3,0x2a,0xe2,0x9f,0xef,0x26,0x00}
    },
    {
        /* No.69 delta:1887 weight:1253 */
        11213,
        64,
        9,
        8,
        {(0x00000000),
         (0x4cbf4fb8),
         (0xcc209ffc),
         (0x809fd044),
         (0x7e700455),
         (0x32cf4bed),
         (0xb2509ba9),
         (0xfeefd411),
         (0x0000612d),
         (0x4cbf2e95),
         (0xcc20fed1),
         (0x809fb169),
         (0x7e706578),
         (0x32cf2ac0),
         (0xb250fa84),
         (0xfeefb53c)},
        {(0x00000000),
         (0x50090000),
         (0x00035000),
         (0x500a5000),
         (0x04019000),
         (0x54089000),
         (0x0402c000),
         (0x540bc000),
         (0x05027e00),
         (0x550b7e00),
         (0x05012e00),
         (0x55082e00),
         (0x0103ee00),
         (0x510aee00),
         (0x0100be00),
         (0x5109be00)},
        {(0x3f800000),
         (0x3fa80480),
         (0x3f8001a8),
         (0x3fa80528),
         (0x3f8200c8),
         (0x3faa0448),
         (0x3f820160),
         (0x3faa05e0),
         (0x3f82813f),
         (0x3faa85bf),
         (0x3f828097),
         (0x3faa8417),
         (0x3f8081f7),
         (0x3fa88577),
         (0x3f80805f),
         (0x3fa884df)},
        (0xfff80000),
        {0x1f,0x11,0x3a,0x53,0x30,0xf5,0x3a,0x92,0x69,0x8f,
         0x5e,0x40,0x86,0x58,0x16,0xa4,0x7f,0x33,0x04,0xc6,0x00}
    },
    {
        /* No.70 delta:1651 weight:1401 */
        11213,
        35,
        9,
        8,
        {(0x00000000),
         (0xcaa73bcd),
         (0x48ffa480),
         (0x82589f4d),
         (0x0b600460),
         (0xc1c73fad),
         (0x439fa0e0),
         (0x89389b2d),
         (0x0000d8fb),
         (0xcaa7e336),
         (0x48ff7c7b),
         (0x825847b6),
         (0x0b60dc9b),
         (0xc1c7e756),
         (0x439f781b),
         (0x893843d6)},
        {(0x00000000),
         (0x90604200),
         (0x20222000),
         (0xb0426200),
         (0x5023a800),
         (0xc043ea00),
         (0x70018800),
         (0xe061ca00),
         (0x16031e00),
         (0x86635c00),
         (0x36213e00),
         (0xa6417c00),
         (0x4620b600),
         (0xd640f400),
         (0x66029600),
         (0xf662d400)},
        {(0x3f800000),
         (0x3fc83021),
         (0x3f901110),
         (0x3fd82131),
         (0x3fa811d4),
         (0x3fe021f5),
         (0x3fb800c4),
         (0x3ff030e5),
         (0x3f8b018f),
         (0x3fc331ae),
         (0x3f9b109f),
         (0x3fd320be),
         (0x3fa3105b),
         (0x3feb207a),
         (0x3fb3014b),
         (0x3ffb316a)},
        (0xfff80000),
        {0xf7,0xac,0x3b,0x82,0x60,0x2e,0xa2,0x38,0xd5,0xbd,
         0x9e,0x07,0x19,0x05,0x37,0x68,0xa8,0x2b,0xcc,0x5b,0x00}
    },
    {
        /* No.71 delta:805 weight:1559 */
        11213,
        85,
        17,
        2,
        {(0x00000000),
         (0x4ea39f4b),
         (0xeedcca5e),
         (0xa07f5515),
         (0x1a80047d),
         (0x54239b36),
         (0xf45cce23),
         (0xbaff5168),
         (0x000051a3),
         (0x4ea3cee8),
         (0xeedc9bfd),
         (0xa07f04b6),
         (0x1a8055de),
         (0x5423ca95),
         (0xf45c9f80),
         (0xbaff00cb)},
        {(0x00000000),
         (0x0879e800),
         (0x10141400),
         (0x186dfc00),
         (0x006c0800),
         (0x0815e000),
         (0x10781c00),
         (0x1801f400),
         (0x40237e00),
         (0x485a9600),
         (0x50376a00),
         (0x584e8200),
         (0x404f7600),
         (0x48369e00),
         (0x505b6200),
         (0x58228a00)},
        {(0x3f800000),
         (0x3f843cf4),
         (0x3f880a0a),
         (0x3f8c36fe),
         (0x3f803604),
         (0x3f840af0),
         (0x3f883c0e),
         (0x3f8c00fa),
         (0x3fa011bf),
         (0x3fa42d4b),
         (0x3fa81bb5),
         (0x3fac2741),
         (0x3fa027bb),
         (0x3fa41b4f),
         (0x3fa82db1),
         (0x3fac1145)},
        (0xfff80000),
        {0x81,0x81,0xe0,0x37,0x51,0x1b,0x87,0x01,0xa3,0x40,
         0x70,0xd0,0xb9,0x63,0xbb,0xc0,0xe9,0x49,0x0b,0x62,0x00}
    },
    {
        /* No.72 delta:1929 weight:1383 */
        11213,
        77,
        8,
        10,
        {(0x00000000),
         (0x9625646a),
         (0xa06390e5),
         (0x3646f48f),
         (0x6c400487),
         (0xfa6560ed),
         (0xcc239462),
         (0x5a06f008),
         (0x00006232),
         (0x96250658),
         (0xa063f2d7),
         (0x364696bd),
         (0x6c4066b5),
         (0xfa6502df),
         (0xcc23f650),
         (0x5a06923a)},
        {(0x00000000),
         (0x60350000),
         (0x08a08000),
         (0x68958000),
         (0x00120000),
         (0x60270000),
         (0x08b28000),
         (0x68878000),
         (0x60021e00),
         (0x00371e00),
         (0x68a29e00),
         (0x08979e00),
         (0x60101e00),
         (0x00251e00),
         (0x68b09e00),
         (0x08859e00)},
        {(0x3f800000),
         (0x3fb01a80),
         (0x3f845040),
         (0x3fb44ac0),
         (0x3f800900),
         (0x3fb01380),
         (0x3f845940),
         (0x3fb443c0),
         (0x3fb0010f),
         (0x3f801b8f),
         (0x3fb4514f),
         (0x3f844bcf),
         (0x3fb0080f),
         (0x3f80128f),
         (0x3fb4584f),
         (0x3f8442cf)},
        (0xfff80000),
        {0xec,0xe8,0xd6,0x3f,0xf0,0x4f,0x6e,0xc9,0x92,0xb9,
         0x0b,0xf3,0x46,0x01,0x83,0x06,0xba,0xbb,0xfc,0x3c,0x00}
    },
    {
        /* No.73 delta:1312 weight:1037 */
        11213,
        79,
        14,
        13,
        {(0x00000000),
         (0x3c3b4194),
         (0x3b4a522a),
         (0x077113be),
         (0x70e00495),
         (0x4cdb4501),
         (0x4baa56bf),
         (0x7791172b),
         (0x00001842),
         (0x3c3b59d6),
         (0x3b4a4a68),
         (0x07710bfc),
         (0x70e01cd7),
         (0x4cdb5d43),
         (0x4baa4efd),
         (0x77910f69)},
        {(0x00000000),
         (0x5e1d3000),
         (0x08002800),
         (0x561d1800),
         (0x00404000),
         (0x5e5d7000),
         (0x08406800),
         (0x565d5800),
         (0xa7841e00),
         (0xf9992e00),
         (0xaf843600),
         (0xf1990600),
         (0xa7c45e00),
         (0xf9d96e00),
         (0xafc47600),
         (0xf1d94600)},
        {(0x3f800000),
         (0x3faf0e98),
         (0x3f840014),
         (0x3fab0e8c),
         (0x3f802020),
         (0x3faf2eb8),
         (0x3f842034),
         (0x3fab2eac),
         (0x3fd3c20f),
         (0x3ffccc97),
         (0x3fd7c21b),
         (0x3ff8cc83),
         (0x3fd3e22f),
         (0x3ffcecb7),
         (0x3fd7e23b),
         (0x3ff8eca3)},
        (0xfff80000),
        {0xe5,0x84,0x28,0x6a,0xc7,0xb8,0xff,0x35,0x7e,0x4a,
         0xd0,0x29,0x99,0x65,0xc9,0xf9,0x00,0xb5,0xa5,0x73,0x00}
    },
    {
        /* No.74 delta:1274 weight:753 */
        11213,
        83,
        10,
        18,
        {(0x00000000),
         (0xdb6fd096),
         (0x7bead936),
         (0xa08509a0),
         (0x059004a2),
         (0xdeffd434),
         (0x7e7add94),
         (0xa5150d02),
         (0x00003dd4),
         (0xdb6fed42),
         (0x7beae4e2),
         (0xa0853474),
         (0x05903976),
         (0xdeffe9e0),
         (0x7e7ae040),
         (0xa51530d6)},
        {(0x00000000),
         (0x0862a000),
         (0x20176000),
         (0x2875c000),
         (0x21182000),
         (0x297a8000),
         (0x010f4000),
         (0x096de000),
         (0x100c1e00),
         (0x186ebe00),
         (0x301b7e00),
         (0x3879de00),
         (0x31143e00),
         (0x39769e00),
         (0x11035e00),
         (0x1961fe00)},
        {(0x3f800000),
         (0x3f843150),
         (0x3f900bb0),
         (0x3f943ae0),
         (0x3f908c10),
         (0x3f94bd40),
         (0x3f8087a0),
         (0x3f84b6f0),
         (0x3f88060f),
         (0x3f8c375f),
         (0x3f980dbf),
         (0x3f9c3cef),
         (0x3f988a1f),
         (0x3f9cbb4f),
         (0x3f8881af),
         (0x3f8cb0ff)},
        (0xfff80000),
        {0x94,0x6b,0xe1,0x97,0xcf,0xd7,0x1d,0x69,0x3d,0xf1,
         0xd3,0x31,0x9b,0x3f,0x74,0x40,0xe4,0x78,0x37,0x93,0x00}
    },
    {
        /* No.75 delta:3240 weight:999 */
        11213,
        25,
        3,
        14,
        {(0x00000000),
         (0x5231ede5),
         (0x75f41157),
         (0x27c5fcb2),
         (0x231004bc),
         (0x7121e959),
         (0x56e415eb),
         (0x04d5f80e),
         (0x000014a2),
         (0x5231f947),
         (0x75f405f5),
         (0x27c5e810),
         (0x2310101e),
         (0x7121fdfb),
         (0x56e40149),
         (0x04d5ecac)},
        {(0x00000000),
         (0x09c10000),
         (0x50f28000),
         (0x59338000),
         (0x12424000),
         (0x1b834000),
         (0x42b0c000),
         (0x4b71c000),
         (0x02361e00),
         (0x0bf71e00),
         (0x52c49e00),
         (0x5b059e00),
         (0x10745e00),
         (0x19b55e00),
         (0x4086de00),
         (0x4947de00)},
        {(0x3f800000),
         (0x3f84e080),
         (0x3fa87940),
         (0x3fac99c0),
         (0x3f892120),
         (0x3f8dc1a0),
         (0x3fa15860),
         (0x3fa5b8e0),
         (0x3f811b0f),
         (0x3f85fb8f),
         (0x3fa9624f),
         (0x3fad82cf),
         (0x3f883a2f),
         (0x3f8cdaaf),
         (0x3fa0436f),
         (0x3fa4a3ef)},
        (0xfff80000),
        {0x22,0xa1,0x8f,0x2e,0x7e,0xb0,0x8f,0x95,0xab,0x7b,
         0xe4,0x15,0x6f,0x5a,0x78,0x2a,0xeb,0x47,0xfe,0xa6,0x00}
    },
    {
        /* No.76 delta:1884 weight:1457 */
        11213,
        11,
        13,
        3,
        {(0x00000000),
         (0xb165bd1d),
         (0x1f5b5cd1),
         (0xae3ee1cc),
         (0x07b004ca),
         (0xb6d5b9d7),
         (0x18eb581b),
         (0xa98ee506),
         (0x00000ebc),
         (0xb165b3a1),
         (0x1f5b526d),
         (0xae3eef70),
         (0x07b00a76),
         (0xb6d5b76b),
         (0x18eb56a7),
         (0xa98eebba)},
        {(0x00000000),
         (0x2a1ef400),
         (0x43091000),
         (0x6917e400),
         (0x05182000),
         (0x2f06d400),
         (0x46113000),
         (0x6c0fc400),
         (0x20c41e00),
         (0x0adaea00),
         (0x63cd0e00),
         (0x49d3fa00),
         (0x25dc3e00),
         (0x0fc2ca00),
         (0x66d52e00),
         (0x4ccbda00)},
        {(0x3f800000),
         (0x3f950f7a),
         (0x3fa18488),
         (0x3fb48bf2),
         (0x3f828c10),
         (0x3f97836a),
         (0x3fa30898),
         (0x3fb607e2),
         (0x3f90620f),
         (0x3f856d75),
         (0x3fb1e687),
         (0x3fa4e9fd),
         (0x3f92ee1f),
         (0x3f87e165),
         (0x3fb36a97),
         (0x3fa665ed)},
        (0xfff80000),
        {0x5f,0x66,0x78,0x0e,0x66,0x3f,0x37,0x9d,0x75,0xa2,
         0x49,0x78,0xb7,0x8c,0xf1,0x7c,0x84,0xf3,0xab,0x84,0x00}
    },
    {
        /* No.77 delta:1037 weight:1439 */
        11213,
        43,
        15,
        7,
        {(0x00000000),
         (0x3b7738f7),
         (0x2ee7f42b),
         (0x1590ccdc),
         (0x8c3004de),
         (0xb7473c29),
         (0xa2d7f0f5),
         (0x99a0c802),
         (0x0000b3f9),
         (0x3b778b0e),
         (0x2ee747d2),
         (0x15907f25),
         (0x8c30b727),
         (0xb7478fd0),
         (0xa2d7430c),
         (0x99a07bfb)},
        {(0x00000000),
         (0x20009400),
         (0x70030800),
         (0x50039c00),
         (0x90011600),
         (0xb0018200),
         (0xe0021e00),
         (0xc0028a00),
         (0x40005e00),
         (0x6000ca00),
         (0x30035600),
         (0x1003c200),
         (0xd0014800),
         (0xf001dc00),
         (0xa0024000),
         (0x8002d400)},
        {(0x3f800000),
         (0x3f90004a),
         (0x3fb80184),
         (0x3fa801ce),
         (0x3fc8008b),
         (0x3fd800c1),
         (0x3ff0010f),
         (0x3fe00145),
         (0x3fa0002f),
         (0x3fb00065),
         (0x3f9801ab),
         (0x3f8801e1),
         (0x3fe800a4),
         (0x3ff800ee),
         (0x3fd00120),
         (0x3fc0016a)},
        (0xfff80000),
        {0x4d,0xd9,0x1c,0xbc,0x61,0x94,0x41,0xb3,0x64,0x5a,
         0xe0,0x27,0x21,0x33,0x58,0x8a,0x3a,0x27,0xe7,0x61,0x00}
    },
    {
        /* No.78 delta:3771 weight:1149 */
        11213,
        67,
        6,
        13,
        {(0x00000000),
         (0xeb935e7e),
         (0x12dd9004),
         (0xf94ece7a),
         (0x518004ec),
         (0xba135a92),
         (0x435d94e8),
         (0xa8ceca96),
         (0x00002915),
         (0xeb93776b),
         (0x12ddb911),
         (0xf94ee76f),
         (0x51802df9),
         (0xba137387),
         (0x435dbdfd),
         (0xa8cee383)},
        {(0x00000000),
         (0x00600000),
         (0x005c0000),
         (0x003c0000),
         (0xa7000000),
         (0xa7600000),
         (0xa75c0000),
         (0xa73c0000),
         (0x00381e00),
         (0x00581e00),
         (0x00641e00),
         (0x00041e00),
         (0xa7381e00),
         (0xa7581e00),
         (0xa7641e00),
         (0xa7041e00)},
        {(0x3f800000),
         (0x3f803000),
         (0x3f802e00),
         (0x3f801e00),
         (0x3fd38000),
         (0x3fd3b000),
         (0x3fd3ae00),
         (0x3fd39e00),
         (0x3f801c0f),
         (0x3f802c0f),
         (0x3f80320f),
         (0x3f80020f),
         (0x3fd39c0f),
         (0x3fd3ac0f),
         (0x3fd3b20f),
         (0x3fd3820f)},
        (0xfff80000),
        {0x61,0xb9,0x79,0x4a,0xf6,0x0b,0x90,0x19,0x41,0xed,
         0xc7,0x02,0x30,0x6e,0x66,0x3a,0xa0,0x21,0x38,0x4d,0x00}
    },
    {
        /* No.79 delta:8905 weight:995 */
        11213,
        37,
        2,
        14,
        {(0x00000000),
         (0xc386d224),
         (0xa19cefca),
         (0x621a3dee),
         (0x73c004f0),
         (0xb046d6d4),
         (0xd25ceb3a),
         (0x11da391e),
         (0x00006023),
         (0xc386b207),
         (0xa19c8fe9),
         (0x621a5dcd),
         (0x73c064d3),
         (0xb046b6f7),
         (0xd25c8b19),
         (0x11da593d)},
        {(0x00000000),
         (0x50000000),
         (0x60000000),
         (0x30000000),
         (0x00000000),
         (0x50000000),
         (0x60000000),
         (0x30000000),
         (0x10001e00),
         (0x40001e00),
         (0x70001e00),
         (0x20001e00),
         (0x10001e00),
         (0x40001e00),
         (0x70001e00),
         (0x20001e00)},
        {(0x3f800000),
         (0x3fa80000),
         (0x3fb00000),
         (0x3f980000),
         (0x3f800000),
         (0x3fa80000),
         (0x3fb00000),
         (0x3f980000),
         (0x3f88000f),
         (0x3fa0000f),
         (0x3fb8000f),
         (0x3f90000f),
         (0x3f88000f),
         (0x3fa0000f),
         (0x3fb8000f),
         (0x3f90000f)},
        (0xfff80000),
        {0xcd,0x77,0xa6,0x4e,0x16,0x97,0x82,0x51,0x40,0x8c,
         0x23,0x3d,0x69,0x41,0x55,0xf4,0x6d,0xb5,0x97,0xc4,0x00}
    },
    {
        /* No.80 delta:1084 weight:1327 */
        11213,
        41,
        20,
        7,
        {(0x00000000),
         (0x03b0124c),
         (0x2709d148),
         (0x24b9c304),
         (0xd150050f),
         (0xd2e01743),
         (0xf659d447),
         (0xf5e9c60b),
         (0x0000e5a0),
         (0x03b0f7ec),
         (0x270934e8),
         (0x24b926a4),
         (0xd150e0af),
         (0xd2e0f2e3),
         (0xf65931e7),
         (0xf5e923ab)},
        {(0x00000000),
         (0x58fc3200),
         (0x70059800),
         (0x28f9aa00),
         (0x00443a00),
         (0x58b80800),
         (0x7041a200),
         (0x28bd9000),
         (0x60225e00),
         (0x38de6c00),
         (0x1027c600),
         (0x48dbf400),
         (0x60666400),
         (0x389a5600),
         (0x1063fc00),
         (0x489fce00)},
        {(0x3f800000),
         (0x3fac7e19),
         (0x3fb802cc),
         (0x3f947cd5),
         (0x3f80221d),
         (0x3fac5c04),
         (0x3fb820d1),
         (0x3f945ec8),
         (0x3fb0112f),
         (0x3f9c6f36),
         (0x3f8813e3),
         (0x3fa46dfa),
         (0x3fb03332),
         (0x3f9c4d2b),
         (0x3f8831fe),
         (0x3fa44fe7)},
        (0xfff80000),
        {0x17,0x06,0xb5,0x56,0x08,0x24,0x02,0x45,0x7d,0xec,
         0xaa,0x6e,0xee,0x42,0x3d,0x61,0xec,0x7b,0x95,0xda,0x00}
    },
    {
        /* No.81 delta:2107 weight:929 */
        11213,
        68,
        7,
        13,
        {(0x00000000),
         (0xa2680084),
         (0x72c07011),
         (0xd0a87095),
         (0xe9c00518),
         (0x4ba8059c),
         (0x9b007509),
         (0x3968758d),
         (0x000019d6),
         (0xa2681952),
         (0x72c069c7),
         (0xd0a86943),
         (0xe9c01cce),
         (0x4ba81c4a),
         (0x9b006cdf),
         (0x39686c5b)},
        {(0x00000000),
         (0x06214000),
         (0x50182000),
         (0x56396000),
         (0x004c0000),
         (0x066d4000),
         (0x50542000),
         (0x56756000),
         (0x003abe00),
         (0x061bfe00),
         (0x50229e00),
         (0x5603de00),
         (0x0076be00),
         (0x0657fe00),
         (0x506e9e00),
         (0x564fde00)},
        {(0x3f800000),
         (0x3f8310a0),
         (0x3fa80c10),
         (0x3fab1cb0),
         (0x3f802600),
         (0x3f8336a0),
         (0x3fa82a10),
         (0x3fab3ab0),
         (0x3f801d5f),
         (0x3f830dff),
         (0x3fa8114f),
         (0x3fab01ef),
         (0x3f803b5f),
         (0x3f832bff),
         (0x3fa8374f),
         (0x3fab27ef)},
        (0xfff80000),
        {0x06,0xc1,0x16,0x3a,0x4b,0xf5,0xcc,0xe4,0x06,0xc5,
         0x47,0x64,0xd8,0x26,0x6e,0xa3,0xd2,0xe3,0xfc,0x67,0x00}
    },
    {
        /* No.82 delta:1653 weight:993 */
        11213,
        67,
        15,
        1,
        {(0x00000000),
         (0x205bddc2),
         (0x51a5fb05),
         (0x71fe26c7),
         (0xbc800525),
         (0x9cdbd8e7),
         (0xed25fe20),
         (0xcd7e23e2),
         (0x0000ae60),
         (0x205b73a2),
         (0x51a55565),
         (0x71fe88a7),
         (0xbc80ab45),
         (0x9cdb7687),
         (0xed255040),
         (0xcd7e8d82)},
        {(0x00000000),
         (0x200d8e00),
         (0x01004000),
         (0x210dce00),
         (0x1710c000),
         (0x371d4e00),
         (0x16108000),
         (0x361d0e00),
         (0x00409e00),
         (0x204d1000),
         (0x0140de00),
         (0x214d5000),
         (0x17505e00),
         (0x375dd000),
         (0x16501e00),
         (0x365d9000)},
        {(0x3f800000),
         (0x3f9006c7),
         (0x3f808020),
         (0x3f9086e7),
         (0x3f8b8860),
         (0x3f9b8ea7),
         (0x3f8b0840),
         (0x3f9b0e87),
         (0x3f80204f),
         (0x3f902688),
         (0x3f80a06f),
         (0x3f90a6a8),
         (0x3f8ba82f),
         (0x3f9baee8),
         (0x3f8b280f),
         (0x3f9b2ec8)},
        (0xfff80000),
        {0x31,0x5a,0x14,0x56,0x22,0x69,0x71,0xb4,0xed,0xe9,
         0x57,0x3c,0xb3,0xa3,0xc2,0xd8,0x5c,0x3b,0xf9,0x7a,0x00}
    },
    {
        /* No.83 delta:2591 weight:1355 */
        11213,
        78,
        3,
        3,
        {(0x00000000),
         (0x3fa6f3ae),
         (0x11561526),
         (0x2ef0e688),
         (0x3f40053d),
         (0x00e6f693),
         (0x2e16101b),
         (0x11b0e3b5),
         (0x000038dd),
         (0x3fa6cb73),
         (0x11562dfb),
         (0x2ef0de55),
         (0x3f403de0),
         (0x00e6ce4e),
         (0x2e1628c6),
         (0x11b0db68)},
        {(0x00000000),
         (0x88264000),
         (0x60420000),
         (0xe8644000),
         (0x20102000),
         (0xa8366000),
         (0x40522000),
         (0xc8746000),
         (0x00791e00),
         (0x885f5e00),
         (0x603b1e00),
         (0xe81d5e00),
         (0x20693e00),
         (0xa84f7e00),
         (0x402b3e00),
         (0xc80d7e00)},
        {(0x3f800000),
         (0x3fc41320),
         (0x3fb02100),
         (0x3ff43220),
         (0x3f900810),
         (0x3fd41b30),
         (0x3fa02910),
         (0x3fe43a30),
         (0x3f803c8f),
         (0x3fc42faf),
         (0x3fb01d8f),
         (0x3ff40eaf),
         (0x3f90349f),
         (0x3fd427bf),
         (0x3fa0159f),
         (0x3fe406bf)},
        (0xfff80000),
        {0x95,0xde,0xea,0xd2,0x91,0x96,0x3a,0x31,0x18,0xb4,
         0x29,0x3c,0x05,0x83,0x13,0x20,0xa6,0xbb,0xfa,0x90,0x00}
    },
    {
        /* No.84 delta:1724 weight:1387 */
        11213,
        31,
        24,
        7,
        {(0x00000000),
         (0x6f77a064),
         (0x9d4e3ea4),
         (0xf2399ec0),
         (0xdab00543),
         (0xb5c7a527),
         (0x47fe3be7),
         (0x28899b83),
         (0x0000ba57),
         (0x6f771a33),
         (0x9d4e84f3),
         (0xf2392497),
         (0xdab0bf14),
         (0xb5c71f70),
         (0x47fe81b0),
         (0x288921d4)},
        {(0x00000000),
         (0x4052c800),
         (0x403d7000),
         (0x006fb800),
         (0x01748400),
         (0x41264c00),
         (0x4149f400),
         (0x011b3c00),
         (0x30183e00),
         (0x704af600),
         (0x70254e00),
         (0x30778600),
         (0x316cba00),
         (0x713e7200),
         (0x7151ca00),
         (0x31030200)},
        {(0x3f800000),
         (0x3fa02964),
         (0x3fa01eb8),
         (0x3f8037dc),
         (0x3f80ba42),
         (0x3fa09326),
         (0x3fa0a4fa),
         (0x3f808d9e),
         (0x3f980c1f),
         (0x3fb8257b),
         (0x3fb812a7),
         (0x3f983bc3),
         (0x3f98b65d),
         (0x3fb89f39),
         (0x3fb8a8e5),
         (0x3f988181)},
        (0xfff80000),
        {0x24,0x42,0xb5,0x8a,0x26,0x25,0x0b,0x43,0xb5,0xa1,
         0xfd,0xf3,0xef,0xe4,0x58,0x1d,0x2f,0x82,0x54,0xe1,0x00}
    },
    {
        /* No.85 delta:1315 weight:1167 */
        11213,
        28,
        18,
        8,
        {(0x00000000),
         (0x7039a7b2),
         (0x655a69d6),
         (0x1563ce64),
         (0x55800552),
         (0x25b9a2e0),
         (0x30da6c84),
         (0x40e3cb36),
         (0x0000db22),
         (0x70397c90),
         (0x655ab2f4),
         (0x15631546),
         (0x5580de70),
         (0x25b979c2),
         (0x30dab7a6),
         (0x40e31014)},
        {(0x00000000),
         (0x091ae000),
         (0xc01fd200),
         (0xc9053200),
         (0x60072600),
         (0x691dc600),
         (0xa018f400),
         (0xa9021400),
         (0x40083e00),
         (0x4912de00),
         (0x8017ec00),
         (0x890d0c00),
         (0x200f1800),
         (0x2915f800),
         (0xe010ca00),
         (0xe90a2a00)},
        {(0x3f800000),
         (0x3f848d70),
         (0x3fe00fe9),
         (0x3fe48299),
         (0x3fb00393),
         (0x3fb48ee3),
         (0x3fd00c7a),
         (0x3fd4810a),
         (0x3fa0041f),
         (0x3fa4896f),
         (0x3fc00bf6),
         (0x3fc48686),
         (0x3f90078c),
         (0x3f948afc),
         (0x3ff00865),
         (0x3ff48515)},
        (0xfff80000),
        {0x54,0x00,0xe5,0x09,0x83,0x8a,0xa3,0xac,0xe2,0x03,
         0xe4,0x5f,0x87,0xaa,0x93,0x3c,0x49,0xef,0xf1,0x12,0x00}
    },
    {
        /* No.86 delta:1805 weight:1023 */
        11213,
        40,
        17,
        1,
        {(0x00000000),
         (0xd727963d),
         (0x760f18b0),
         (0xa1288e8d),
         (0xe2d00563),
         (0x35f7935e),
         (0x94df1dd3),
         (0x43f88bee),
         (0x0000c942),
         (0xd7275f7f),
         (0x760fd1f2),
         (0xa12847cf),
         (0xe2d0cc21),
         (0x35f75a1c),
         (0x94dfd491),
         (0x43f842ac)},
        {(0x00000000),
         (0x207c0400),
         (0x56934000),
         (0x76ef4400),
         (0x00009e00),
         (0x207c9a00),
         (0x5693de00),
         (0x76efda00),
         (0x20009e00),
         (0x007c9a00),
         (0x7693de00),
         (0x56efda00),
         (0x20000000),
         (0x007c0400),
         (0x76934000),
         (0x56ef4400)},
        {(0x3f800000),
         (0x3f903e02),
         (0x3fab49a0),
         (0x3fbb77a2),
         (0x3f80004f),
         (0x3f903e4d),
         (0x3fab49ef),
         (0x3fbb77ed),
         (0x3f90004f),
         (0x3f803e4d),
         (0x3fbb49ef),
         (0x3fab77ed),
         (0x3f900000),
         (0x3f803e02),
         (0x3fbb49a0),
         (0x3fab77a2)},
        (0xfff80000),
        {0xc8,0x53,0x9a,0xab,0x96,0xd8,0xe7,0x02,0xe6,0xf1,
         0x74,0x36,0x00,0x32,0x32,0x6e,0x36,0xe4,0x78,0x88,0x00}
    },
    {
        /* No.87 delta:2165 weight:1423 */
        11213,
        29,
        6,
        7,
        {(0x00000000),
         (0xb0a4f75d),
         (0xd99a0020),
         (0x693ef77d),
         (0x0010057f),
         (0xb0b4f222),
         (0xd98a055f),
         (0x692ef202),
         (0x00002860),
         (0xb0a4df3d),
         (0xd99a2840),
         (0x693edf1d),
         (0x00102d1f),
         (0xb0b4da42),
         (0xd98a2d3f),
         (0x692eda62)},
        {(0x00000000),
         (0x00c02000),
         (0x10102000),
         (0x10d00000),
         (0x700c0000),
         (0x70cc2000),
         (0x601c2000),
         (0x60dc0000),
         (0x00701e00),
         (0x00b03e00),
         (0x10603e00),
         (0x10a01e00),
         (0x707c1e00),
         (0x70bc3e00),
         (0x606c3e00),
         (0x60ac1e00)},
        {(0x3f800000),
         (0x3f806010),
         (0x3f880810),
         (0x3f886800),
         (0x3fb80600),
         (0x3fb86610),
         (0x3fb00e10),
         (0x3fb06e00),
         (0x3f80380f),
         (0x3f80581f),
         (0x3f88301f),
         (0x3f88500f),
         (0x3fb83e0f),
         (0x3fb85e1f),
         (0x3fb0361f),
         (0x3fb0560f)},
        (0xfff80000),
        {0xc8,0x26,0xe1,0xb8,0xbd,0xd2,0x06,0x99,0x21,0x8b,
         0xa2,0x82,0x21,0x08,0x66,0x43,0x39,0x7d,0x22,0x3f,0x00}
    },
    {
        /* No.88 delta:2728 weight:929 */
        11213,
        9,
        5,
        12,
        {(0x00000000),
         (0xba91b8b7),
         (0x1f94657f),
         (0xa505ddc8),
         (0xc8900581),
         (0x7201bd36),
         (0xd70460fe),
         (0x6d95d849),
         (0x00009507),
         (0xba912db0),
         (0x1f94f078),
         (0xa50548cf),
         (0xc8909086),
         (0x72012831),
         (0xd704f5f9),
         (0x6d954d4e)},
        {(0x00000000),
         (0x74d60000),
         (0x48810000),
         (0x3c570000),
         (0x07840000),
         (0x73520000),
         (0x4f050000),
         (0x3bd30000),
         (0xa0019e00),
         (0xd4d79e00),
         (0xe8809e00),
         (0x9c569e00),
         (0xa7859e00),
         (0xd3539e00),
         (0xef049e00),
         (0x9bd29e00)},
        {(0x3f800000),
         (0x3fba6b00),
         (0x3fa44080),
         (0x3f9e2b80),
         (0x3f83c200),
         (0x3fb9a900),
         (0x3fa78280),
         (0x3f9de980),
         (0x3fd000cf),
         (0x3fea6bcf),
         (0x3ff4404f),
         (0x3fce2b4f),
         (0x3fd3c2cf),
         (0x3fe9a9cf),
         (0x3ff7824f),
         (0x3fcde94f)},
        (0xfff80000),
        {0xef,0x82,0x25,0x92,0x4d,0x84,0x32,0xcc,0x51,0x79,
         0xa6,0xf1,0xa8,0xb8,0xd3,0xc8,0xf4,0x1f,0x61,0x14,0x00}
    },
    {
        /* No.89 delta:1302 weight:829 */
        11213,
        52,
        17,
        15,
        {(0x00000000),
         (0xff9c667b),
         (0x076e42cb),
         (0xf8f224b0),
         (0x5a200593),
         (0xa5bc63e8),
         (0x5d4e4758),
         (0xa2d22123),
         (0x0000fbe9),
         (0xff9c9d92),
         (0x076eb922),
         (0xf8f2df59),
         (0x5a20fe7a),
         (0xa5bc9801),
         (0x5d4ebcb1),
         (0xa2d2daca)},
        {(0x00000000),
         (0x168c2000),
         (0x08819000),
         (0x1e0db000),
         (0x8400d800),
         (0x928cf800),
         (0x8c814800),
         (0x9a0d6800),
         (0x0a023e00),
         (0x1c8e1e00),
         (0x0283ae00),
         (0x140f8e00),
         (0x8e02e600),
         (0x988ec600),
         (0x86837600),
         (0x900f5600)},
        {(0x3f800000),
         (0x3f8b4610),
         (0x3f8440c8),
         (0x3f8f06d8),
         (0x3fc2006c),
         (0x3fc9467c),
         (0x3fc640a4),
         (0x3fcd06b4),
         (0x3f85011f),
         (0x3f8e470f),
         (0x3f8141d7),
         (0x3f8a07c7),
         (0x3fc70173),
         (0x3fcc4763),
         (0x3fc341bb),
         (0x3fc807ab)},
        (0xfff80000),
        {0x44,0x22,0x55,0x0c,0x46,0x02,0x05,0x6b,0x49,0xa7,
         0x79,0xcc,0xa0,0x5b,0x5c,0xeb,0x82,0xb3,0x48,0x1e,0x00}
    },
    {
        /* No.90 delta:1794 weight:683 */
        11213,
        71,
        7,
        3,
        {(0x00000000),
         (0x28fcb37c),
         (0x3de27e63),
         (0x151ecd1f),
         (0x99d005af),
         (0xb12cb6d3),
         (0xa4327bcc),
         (0x8ccec8b0),
         (0x0000473d),
         (0x28fcf441),
         (0x3de2395e),
         (0x151e8a22),
         (0x99d04292),
         (0xb12cf1ee),
         (0xa4323cf1),
         (0x8cce8f8d)},
        {(0x00000000),
         (0x00170000),
         (0x00059200),
         (0x00129200),
         (0x61088000),
         (0x611f8000),
         (0x610d1200),
         (0x611a1200),
         (0x22825e00),
         (0x22955e00),
         (0x2287cc00),
         (0x2290cc00),
         (0x438ade00),
         (0x439dde00),
         (0x438f4c00),
         (0x43984c00)},
        {(0x3f800000),
         (0x3f800b80),
         (0x3f8002c9),
         (0x3f800949),
         (0x3fb08440),
         (0x3fb08fc0),
         (0x3fb08689),
         (0x3fb08d09),
         (0x3f91412f),
         (0x3f914aaf),
         (0x3f9143e6),
         (0x3f914866),
         (0x3fa1c56f),
         (0x3fa1ceef),
         (0x3fa1c7a6),
         (0x3fa1cc26)},
        (0xfff80000),
        {0x63,0x96,0x53,0x66,0x89,0x8f,0x5a,0x0f,0x8c,0x80,
         0x0a,0x9a,0x17,0xd9,0x66,0x91,0xf8,0x92,0x97,0x62,0x00}
    },
    {
        /* No.91 delta:10527 weight:393 */
        11213,
        5,
        2,
        19,
        {(0x00000000),
         (0x9ea45304),
         (0x56ae02a1),
         (0xc80a51a5),
         (0x9ad005b9),
         (0x047456bd),
         (0xcc7e0718),
         (0x52da541c),
         (0x0000c554),
         (0x9ea49650),
         (0x56aec7f5),
         (0xc80a94f1),
         (0x9ad0c0ed),
         (0x047493e9),
         (0xcc7ec24c),
         (0x52da9148)},
        {(0x00000000),
         (0x64000000),
         (0x0f800000),
         (0x6b800000),
         (0x00000000),
         (0x64000000),
         (0x0f800000),
         (0x6b800000),
         (0x00001e00),
         (0x64001e00),
         (0x0f801e00),
         (0x6b801e00),
         (0x00001e00),
         (0x64001e00),
         (0x0f801e00),
         (0x6b801e00)},
        {(0x3f800000),
         (0x3fb20000),
         (0x3f87c000),
         (0x3fb5c000),
         (0x3f800000),
         (0x3fb20000),
         (0x3f87c000),
         (0x3fb5c000),
         (0x3f80000f),
         (0x3fb2000f),
         (0x3f87c00f),
         (0x3fb5c00f),
         (0x3f80000f),
         (0x3fb2000f),
         (0x3f87c00f),
         (0x3fb5c00f)},
        (0xfff80000),
        {0xd2,0x73,0xa3,0x8b,0x1a,0x90,0xef,0x94,0xe3,0x4a,
         0x16,0x2f,0x38,0x48,0xd9,0x68,0xf2,0xdf,0xe5,0xab,0x00}
    },
    {
        /* No.92 delta:2494 weight:1233 */
        11213,
        7,
        21,
        4,
        {(0x00000000),
         (0x67c6fdcf),
         (0x39165a15),
         (0x5ed0a7da),
         (0xa11005c0),
         (0xc6d6f80f),
         (0x98065fd5),
         (0xffc0a21a),
         (0x0000cdee),
         (0x67c63021),
         (0x391697fb),
         (0x5ed06a34),
         (0xa110c82e),
         (0xc6d635e1),
         (0x9806923b),
         (0xffc06ff4)},
        {(0x00000000),
         (0xc64d0800),
         (0x67f58000),
         (0xa1b88800),
         (0x11ca8200),
         (0xd7878a00),
         (0x763f0200),
         (0xb0720a00),
         (0x4eb51e00),
         (0x88f81600),
         (0x29409e00),
         (0xef0d9600),
         (0x5f7f9c00),
         (0x99329400),
         (0x388a1c00),
         (0xfec71400)},
        {(0x3f800000),
         (0x3fe32684),
         (0x3fb3fac0),
         (0x3fd0dc44),
         (0x3f88e541),
         (0x3febc3c5),
         (0x3fbb1f81),
         (0x3fd83905),
         (0x3fa75a8f),
         (0x3fc47c0b),
         (0x3f94a04f),
         (0x3ff786cb),
         (0x3fafbfce),
         (0x3fcc994a),
         (0x3f9c450e),
         (0x3fff638a)},
        (0xfff80000),
        {0x9e,0xa5,0x58,0x0a,0xe1,0x2c,0x68,0x94,0x2b,0x3f,
         0x4b,0xcd,0xe6,0xee,0x21,0x60,0xf3,0xf9,0x28,0xef,0x00}
    },
    {
        /* No.93 delta:2858 weight:713 */
        11213,
        6,
        11,
        15,
        {(0x00000000),
         (0xce341fc2),
         (0x74808815),
         (0xbab497d7),
         (0x396005de),
         (0xf7541a1c),
         (0x4de08dcb),
         (0x83d49209),
         (0x0000dee4),
         (0xce34c126),
         (0x748056f1),
         (0xbab44933),
         (0x3960db3a),
         (0xf754c4f8),
         (0x4de0532f),
         (0x83d44ced)},
        {(0x00000000),
         (0x0af25000),
         (0x0c419c00),
         (0x06b3cc00),
         (0x82144400),
         (0x88e61400),
         (0x8e55d800),
         (0x84a78800),
         (0xe0053e00),
         (0xeaf76e00),
         (0xec44a200),
         (0xe6b6f200),
         (0x62117a00),
         (0x68e32a00),
         (0x6e50e600),
         (0x64a2b600)},
        {(0x3f800000),
         (0x3f857928),
         (0x3f8620ce),
         (0x3f8359e6),
         (0x3fc10a22),
         (0x3fc4730a),
         (0x3fc72aec),
         (0x3fc253c4),
         (0x3ff0029f),
         (0x3ff57bb7),
         (0x3ff62251),
         (0x3ff35b79),
         (0x3fb108bd),
         (0x3fb47195),
         (0x3fb72873),
         (0x3fb2515b)},
        (0xfff80000),
        {0x67,0xfb,0x7b,0xdf,0x91,0xd7,0xa6,0xcb,0xfa,0x0c,
         0x9c,0xcf,0x32,0x59,0xd4,0x14,0x88,0x02,0x20,0xbd,0x00}
    },
    {
        /* No.94 delta:1428 weight:871 */
        11213,
        51,
        24,
        3,
        {(0x00000000),
         (0x4055c0f5),
         (0x3f9446d5),
         (0x7fc18620),
         (0xbd5005e5),
         (0xfd05c510),
         (0x82c44330),
         (0xc29183c5),
         (0x0000a8c2),
         (0x40556837),
         (0x3f94ee17),
         (0x7fc12ee2),
         (0xbd50ad27),
         (0xfd056dd2),
         (0x82c4ebf2),
         (0xc2912b07)},
        {(0x00000000),
         (0x6064a000),
         (0x4ef89000),
         (0x2e9c3000),
         (0x08a1d000),
         (0x68c57000),
         (0x46594000),
         (0x263de000),
         (0x0c127e00),
         (0x6c76de00),
         (0x42eaee00),
         (0x228e4e00),
         (0x04b3ae00),
         (0x64d70e00),
         (0x4a4b3e00),
         (0x2a2f9e00)},
        {(0x3f800000),
         (0x3fb03250),
         (0x3fa77c48),
         (0x3f974e18),
         (0x3f8450e8),
         (0x3fb462b8),
         (0x3fa32ca0),
         (0x3f931ef0),
         (0x3f86093f),
         (0x3fb63b6f),
         (0x3fa17577),
         (0x3f914727),
         (0x3f8259d7),
         (0x3fb26b87),
         (0x3fa5259f),
         (0x3f9517cf)},
        (0xfff80000),
        {0xfc,0xe1,0x89,0x6f,0x64,0xe4,0x4b,0x42,0xe3,0x27,
         0xa3,0x38,0x46,0x33,0x4f,0xf6,0x63,0x31,0xbb,0x80,0x00}
    },
    {
        /* No.95 delta:1914 weight:999 */
        11213,
        43,
        8,
        13,
        {(0x00000000),
         (0x9c0723c5),
         (0xc882db89),
         (0x5485f84c),
         (0x994005f3),
         (0x05472636),
         (0x51c2de7a),
         (0xcdc5fdbf),
         (0x000019bd),
         (0x9c073a78),
         (0xc882c234),
         (0x5485e1f1),
         (0x99401c4e),
         (0x05473f8b),
         (0x51c2c7c7),
         (0xcdc5e402)},
        {(0x00000000),
         (0x5061f000),
         (0x105bc000),
         (0x403a3000),
         (0x00740400),
         (0x5015f400),
         (0x102fc400),
         (0x404e3400),
         (0x041c1e00),
         (0x547dee00),
         (0x1447de00),
         (0x44262e00),
         (0x04681a00),
         (0x5409ea00),
         (0x1433da00),
         (0x44522a00)},
        {(0x3f800000),
         (0x3fa830f8),
         (0x3f882de0),
         (0x3fa01d18),
         (0x3f803a02),
         (0x3fa80afa),
         (0x3f8817e2),
         (0x3fa0271a),
         (0x3f820e0f),
         (0x3faa3ef7),
         (0x3f8a23ef),
         (0x3fa21317),
         (0x3f82340d),
         (0x3faa04f5),
         (0x3f8a19ed),
         (0x3fa22915)},
        (0xfff80000),
        {0xbf,0x5e,0xb4,0x48,0x47,0x8e,0xcb,0x36,0x4b,0x6a,
         0xc7,0x2c,0x1c,0xc9,0xdc,0x0d,0x4b,0x46,0x15,0xdf,0x00}
    },
    {
        /* No.96 delta:1078 weight:1383 */
        11213,
        93,
        22,
        5,
        {(0x00000000),
         (0x4598c5d2),
         (0x2a3c70f7),
         (0x6fa4b525),
         (0xf4e0060f),
         (0xb178c3dd),
         (0xdedc76f8),
         (0x9b44b32a),
         (0x0000ee71),
         (0x45982ba3),
         (0x2a3c9e86),
         (0x6fa45b54),
         (0xf4e0e87e),
         (0xb1782dac),
         (0xdedc9889),
         (0x9b445d5b)},
        {(0x00000000),
         (0x40220000),
         (0x20738000),
         (0x60518000),
         (0x007e2000),
         (0x405c2000),
         (0x200da000),
         (0x602fa000),
         (0x80035e00),
         (0xc0215e00),
         (0xa070de00),
         (0xe052de00),
         (0x807d7e00),
         (0xc05f7e00),
         (0xa00efe00),
         (0xe02cfe00)},
        {(0x3f800000),
         (0x3fa01100),
         (0x3f9039c0),
         (0x3fb028c0),
         (0x3f803f10),
         (0x3fa02e10),
         (0x3f9006d0),
         (0x3fb017d0),
         (0x3fc001af),
         (0x3fe010af),
         (0x3fd0386f),
         (0x3ff0296f),
         (0x3fc03ebf),
         (0x3fe02fbf),
         (0x3fd0077f),
         (0x3ff0167f)},
        (0xfff80000),
        {0xf7,0x01,0xdc,0x94,0xdb,0xfe,0x75,0xe3,0x41,0x0d,
         0xa7,0xcf,0xdb,0xc4,0xf8,0x63,0x39,0xf1,0x73,0x83,0x00}
    },
    {
        /* No.97 delta:1680 weight:855 */
        11213,
        56,
        11,
        18,
        {(0x00000000),
         (0xed377fcb),
         (0x935b7bcf),
         (0x7e6c0404),
         (0xb1b0061d),
         (0x5c8779d6),
         (0x22eb7dd2),
         (0xcfdc0219),
         (0x0000dc19),
         (0xed37a3d2),
         (0x935ba7d6),
         (0x7e6cd81d),
         (0xb1b0da04),
         (0x5c87a5cf),
         (0x22eba1cb),
         (0xcfdcde00)},
        {(0x00000000),
         (0x10100000),
         (0x201c0000),
         (0x300c0000),
         (0x30000000),
         (0x20100000),
         (0x101c0000),
         (0x000c0000),
         (0x40051e00),
         (0x50151e00),
         (0x60191e00),
         (0x70091e00),
         (0x70051e00),
         (0x60151e00),
         (0x50191e00),
         (0x40091e00)},
        {(0x3f800000),
         (0x3f880800),
         (0x3f900e00),
         (0x3f980600),
         (0x3f980000),
         (0x3f900800),
         (0x3f880e00),
         (0x3f800600),
         (0x3fa0028f),
         (0x3fa80a8f),
         (0x3fb00c8f),
         (0x3fb8048f),
         (0x3fb8028f),
         (0x3fb00a8f),
         (0x3fa80c8f),
         (0x3fa0048f)},
        (0xfff80000),
        {0xa7,0x90,0x32,0xf0,0xe5,0x54,0x81,0x86,0xda,0xa2,
         0x04,0x5e,0xda,0xe5,0xcd,0x25,0x97,0x20,0x5f,0xd8,0x00}
    },
    {
        /* No.98 delta:4818 weight:1887 */
        11213,
        73,
        1,
        4,
        {(0x00000000),
         (0xf3234424),
         (0xe4236cf9),
         (0x170028dd),
         (0x62d00623),
         (0x91f34207),
         (0x86f36ada),
         (0x75d02efe),
         (0x00003707),
         (0xf3237323),
         (0xe4235bfe),
         (0x17001fda),
         (0x62d03124),
         (0x91f37500),
         (0x86f35ddd),
         (0x75d019f9)},
        {(0x00000000),
         (0x40550000),
         (0x40300000),
         (0x00650000),
         (0x7a81c000),
         (0x3ad4c000),
         (0x3ab1c000),
         (0x7ae4c000),
         (0x54351e00),
         (0x14601e00),
         (0x14051e00),
         (0x54501e00),
         (0x2eb4de00),
         (0x6ee1de00),
         (0x6e84de00),
         (0x2ed1de00)},
        {(0x3f800000),
         (0x3fa02a80),
         (0x3fa01800),
         (0x3f803280),
         (0x3fbd40e0),
         (0x3f9d6a60),
         (0x3f9d58e0),
         (0x3fbd7260),
         (0x3faa1a8f),
         (0x3f8a300f),
         (0x3f8a028f),
         (0x3faa280f),
         (0x3f975a6f),
         (0x3fb770ef),
         (0x3fb7426f),
         (0x3f9768ef)},
        (0xfff80000),
        {0x78,0x4c,0x48,0x62,0xcb,0x43,0x6e,0xae,0x79,0x3f,
         0xa2,0x90,0xc7,0xd5,0xf6,0x2c,0x3a,0x94,0xf6,0x88,0x00}
    },
    {
        /* No.99 delta:4941 weight:807 */
        11213,
        35,
        6,
        17,
        {(0x00000000),
         (0xceb13d37),
         (0x6cab25b0),
         (0xa21a1887),
         (0x2d100639),
         (0xe3a13b0e),
         (0x41bb2389),
         (0x8f0a1ebe),
         (0x00002bd0),
         (0xceb116e7),
         (0x6cab0e60),
         (0xa21a3357),
         (0x2d102de9),
         (0xe3a110de),
         (0x41bb0859),
         (0x8f0a356e)},
        {(0x00000000),
         (0x07201000),
         (0x42300200),
         (0x45101200),
         (0x40000000),
         (0x47201000),
         (0x02300200),
         (0x05101200),
         (0x70001e00),
         (0x77200e00),
         (0x32301c00),
         (0x35100c00),
         (0x30001e00),
         (0x37200e00),
         (0x72301c00),
         (0x75100c00)},
        {(0x3f800000),
         (0x3f839008),
         (0x3fa11801),
         (0x3fa28809),
         (0x3fa00000),
         (0x3fa39008),
         (0x3f811801),
         (0x3f828809),
         (0x3fb8000f),
         (0x3fbb9007),
         (0x3f99180e),
         (0x3f9a8806),
         (0x3f98000f),
         (0x3f9b9007),
         (0x3fb9180e),
         (0x3fba8806)},
        (0xfff80000),
        {0x47,0x35,0x44,0x50,0x63,0x4a,0x6a,0xcd,0x53,0x03,
         0xdd,0x48,0x13,0x12,0x80,0xdf,0x0e,0x77,0xfa,0xf3,0x00}
    },
    {
        /* No.100 delta:1308 weight:863 */
        11213,
        86,
        13,
        14,
        {(0x00000000),
         (0xc5ba19cb),
         (0x67d04c0f),
         (0xa26a55c4),
         (0x36300642),
         (0xf38a1f89),
         (0x51e04a4d),
         (0x945a5386),
         (0x0000f011),
         (0xc5bae9da),
         (0x67d0bc1e),
         (0xa26aa5d5),
         (0x3630f653),
         (0xf38aef98),
         (0x51e0ba5c),
         (0x945aa397)},
        {(0x00000000),
         (0x09233a00),
         (0x40645e00),
         (0x49476400),
         (0x1030a000),
         (0x19139a00),
         (0x5054fe00),
         (0x5977c400),
         (0x30c05e00),
         (0x39e36400),
         (0x70a40000),
         (0x79873a00),
         (0x20f0fe00),
         (0x29d3c400),
         (0x6094a000),
         (0x69b79a00)},
        {(0x3f800000),
         (0x3f84919d),
         (0x3fa0322f),
         (0x3fa4a3b2),
         (0x3f881850),
         (0x3f8c89cd),
         (0x3fa82a7f),
         (0x3facbbe2),
         (0x3f98602f),
         (0x3f9cf1b2),
         (0x3fb85200),
         (0x3fbcc39d),
         (0x3f90787f),
         (0x3f94e9e2),
         (0x3fb04a50),
         (0x3fb4dbcd)},
        (0xfff80000),
        {0xbe,0xe1,0xe6,0x46,0x8d,0x04,0x11,0x44,0x1a,0x37,
         0x82,0xd9,0x6f,0xd6,0x9e,0xd5,0x25,0x58,0x1e,0xae,0x00}
    },
    {
        /* No.101 delta:3747 weight:693 */
        11213,
        93,
        7,
        18,
        {(0x00000000),
         (0x34b395ad),
         (0x437cf2d0),
         (0x77cf677d),
         (0xa770065c),
         (0x93c393f1),
         (0xe40cf48c),
         (0xd0bf6121),
         (0x0000f47f),
         (0x34b361d2),
         (0x437c06af),
         (0x77cf9302),
         (0xa770f223),
         (0x93c3678e),
         (0xe40c00f3),
         (0xd0bf955e)},
        {(0x00000000),
         (0x40700000),
         (0x00580000),
         (0x40280000),
         (0x40680000),
         (0x00180000),
         (0x40300000),
         (0x00400000),
         (0x21601e00),
         (0x61101e00),
         (0x21381e00),
         (0x61481e00),
         (0x61081e00),
         (0x21781e00),
         (0x61501e00),
         (0x21201e00)},
        {(0x3f800000),
         (0x3fa03800),
         (0x3f802c00),
         (0x3fa01400),
         (0x3fa03400),
         (0x3f800c00),
         (0x3fa01800),
         (0x3f802000),
         (0x3f90b00f),
         (0x3fb0880f),
         (0x3f909c0f),
         (0x3fb0a40f),
         (0x3fb0840f),
         (0x3f90bc0f),
         (0x3fb0a80f),
         (0x3f90900f)},
        (0xfff80000),
        {0xe2,0xf1,0x5a,0xc6,0xba,0x8b,0xae,0x08,0x43,0xcc,
         0x54,0xa6,0xe9,0x8f,0x81,0x0a,0x85,0xe3,0x5e,0xef,0x00}
    },
    {
        /* No.102 delta:3462 weight:1273 */
        11213,
        55,
        23,
        8,
        {(0x00000000),
         (0xc3cc98c8),
         (0xca3b10a5),
         (0x09f7886d),
         (0x18300660),
         (0xdbfc9ea8),
         (0xd20b16c5),
         (0x11c78e0d),
         (0x000030c4),
         (0xc3cca80c),
         (0xca3b2061),
         (0x09f7b8a9),
         (0x183036a4),
         (0xdbfcae6c),
         (0xd20b2601),
         (0x11c7bec9)},
        {(0x00000000),
         (0x08000000),
         (0x00800000),
         (0x08800000),
         (0x88200000),
         (0x80200000),
         (0x88a00000),
         (0x80a00000),
         (0x03201e00),
         (0x0b201e00),
         (0x03a01e00),
         (0x0ba01e00),
         (0x8b001e00),
         (0x83001e00),
         (0x8b801e00),
         (0x83801e00)},
        {(0x3f800000),
         (0x3f840000),
         (0x3f804000),
         (0x3f844000),
         (0x3fc41000),
         (0x3fc01000),
         (0x3fc45000),
         (0x3fc05000),
         (0x3f81900f),
         (0x3f85900f),
         (0x3f81d00f),
         (0x3f85d00f),
         (0x3fc5800f),
         (0x3fc1800f),
         (0x3fc5c00f),
         (0x3fc1c00f)},
        (0xfff80000),
        {0x43,0x1f,0x6f,0x01,0x80,0xf4,0x2e,0x31,0xcd,0xc1,
         0xb9,0x2f,0x6b,0x5c,0x0e,0x0b,0x35,0xa5,0x70,0x7e,0x00}
    },
    {
        /* No.103 delta:5054 weight:1635 */
        11213,
        64,
        1,
        2,
        {(0x00000000),
         (0x4f30581d),
         (0x5542c11f),
         (0x1a729902),
         (0x9da00670),
         (0xd2905e6d),
         (0xc8e2c76f),
         (0x87d29f72),
         (0x00007e47),
         (0x4f30265a),
         (0x5542bf58),
         (0x1a72e745),
         (0x9da07837),
         (0xd290202a),
         (0xc8e2b928),
         (0x87d2e135)},
        {(0x00000000),
         (0x0be21000),
         (0x55018000),
         (0x5ee39000),
         (0x83944800),
         (0x88765800),
         (0xd695c800),
         (0xdd77d800),
         (0x00669e00),
         (0x0b848e00),
         (0x55671e00),
         (0x5e850e00),
         (0x83f2d600),
         (0x8810c600),
         (0xd6f35600),
         (0xdd114600)},
        {(0x3f800000),
         (0x3f85f108),
         (0x3faa80c0),
         (0x3faf71c8),
         (0x3fc1ca24),
         (0x3fc43b2c),
         (0x3feb4ae4),
         (0x3feebbec),
         (0x3f80334f),
         (0x3f85c247),
         (0x3faab38f),
         (0x3faf4287),
         (0x3fc1f96b),
         (0x3fc40863),
         (0x3feb79ab),
         (0x3fee88a3)},
        (0xfff80000),
        {0x44,0x10,0x9f,0x8c,0xd9,0xbc,0x8e,0xb6,0xd5,0x14,
         0x72,0x32,0xdd,0xf2,0x3c,0xea,0xb3,0x38,0xb9,0x31,0x00}
    },
    {
        /* No.104 delta:2496 weight:907 */
        11213,
        79,
        15,
        14,
        {(0x00000000),
         (0x5c0202d2),
         (0x1c3f4858),
         (0x403d4a8a),
         (0xd2f0068f),
         (0x8ef2045d),
         (0xcecf4ed7),
         (0x92cd4c05),
         (0x0000d76d),
         (0x5c02d5bf),
         (0x1c3f9f35),
         (0x403d9de7),
         (0xd2f0d1e2),
         (0x8ef2d330),
         (0xcecf99ba),
         (0x92cd9b68)},
        {(0x00000000),
         (0x6b8d0400),
         (0x1053a000),
         (0x7bdea400),
         (0x19807000),
         (0x720d7400),
         (0x09d3d000),
         (0x625ed400),
         (0x08441e00),
         (0x63c91a00),
         (0x1817be00),
         (0x739aba00),
         (0x11c46e00),
         (0x7a496a00),
         (0x0197ce00),
         (0x6a1aca00)},
        {(0x3f800000),
         (0x3fb5c682),
         (0x3f8829d0),
         (0x3fbdef52),
         (0x3f8cc038),
         (0x3fb906ba),
         (0x3f84e9e8),
         (0x3fb12f6a),
         (0x3f84220f),
         (0x3fb1e48d),
         (0x3f8c0bdf),
         (0x3fb9cd5d),
         (0x3f88e237),
         (0x3fbd24b5),
         (0x3f80cbe7),
         (0x3fb50d65)},
        (0xfff80000),
        {0xb7,0xe4,0x3c,0x74,0x28,0xb1,0x2f,0xa4,0x5c,0x37,
         0xb1,0x69,0xd9,0x95,0x83,0xd9,0xa6,0x36,0x5b,0x12,0x00}
    },
    {
        /* No.105 delta:1174 weight:1019 */
        11213,
        91,
        17,
        14,
        {(0x00000000),
         (0xa83f2177),
         (0xa0b43c75),
         (0x088b1d02),
         (0x43300696),
         (0xeb0f27e1),
         (0xe3843ae3),
         (0x4bbb1b94),
         (0x00007f34),
         (0xa83f5e43),
         (0xa0b44341),
         (0x088b6236),
         (0x433079a2),
         (0xeb0f58d5),
         (0xe38445d7),
         (0x4bbb64a0)},
        {(0x00000000),
         (0x0115c000),
         (0x90090000),
         (0x911cc000),
         (0x10422000),
         (0x1157e000),
         (0x804b2000),
         (0x815ee000),
         (0x10393e00),
         (0x112cfe00),
         (0x80303e00),
         (0x8125fe00),
         (0x007b1e00),
         (0x016ede00),
         (0x90721e00),
         (0x9167de00)},
        {(0x3f800000),
         (0x3f808ae0),
         (0x3fc80480),
         (0x3fc88e60),
         (0x3f882110),
         (0x3f88abf0),
         (0x3fc02590),
         (0x3fc0af70),
         (0x3f881c9f),
         (0x3f88967f),
         (0x3fc0181f),
         (0x3fc092ff),
         (0x3f803d8f),
         (0x3f80b76f),
         (0x3fc8390f),
         (0x3fc8b3ef)},
        (0xfff80000),
        {0xe2,0x4f,0x2f,0xcb,0x05,0x76,0xce,0x0b,0xc7,0x7b,
         0xcc,0x65,0x06,0xce,0x2c,0x0c,0x3c,0xb6,0x2c,0x79,0x00}
    },
    {
        /* No.106 delta:1564 weight:925 */
        11213,
        40,
        9,
        1,
        {(0x00000000),
         (0xd288f412),
         (0x0b33c7cb),
         (0xd9bb33d9),
         (0xe0d006a8),
         (0x3258f2ba),
         (0xebe3c163),
         (0x396b3571),
         (0x00004470),
         (0xd288b062),
         (0x0b3383bb),
         (0xd9bb77a9),
         (0xe0d042d8),
         (0x3258b6ca),
         (0xebe38513),
         (0x396b7101)},
        {(0x00000000),
         (0x46794000),
         (0x004d0000),
         (0x46344000),
         (0x007f0200),
         (0x46064200),
         (0x00320200),
         (0x464b4200),
         (0x00235e00),
         (0x465a1e00),
         (0x006e5e00),
         (0x46171e00),
         (0x005c5c00),
         (0x46251c00),
         (0x00115c00),
         (0x46681c00)},
        {(0x3f800000),
         (0x3fa33ca0),
         (0x3f802680),
         (0x3fa31a20),
         (0x3f803f81),
         (0x3fa30321),
         (0x3f801901),
         (0x3fa325a1),
         (0x3f8011af),
         (0x3fa32d0f),
         (0x3f80372f),
         (0x3fa30b8f),
         (0x3f802e2e),
         (0x3fa3128e),
         (0x3f8008ae),
         (0x3fa3340e)},
        (0xfff80000),
        {0xc1,0x6e,0x8a,0x33,0x35,0x9f,0x07,0x03,0x76,0x11,
         0xe5,0x21,0xc3,0xdb,0x18,0x40,0x05,0xa0,0xd6,0x4c,0x00}
    },
    {
        /* No.107 delta:3386 weight:1851 */
        11213,
        48,
        2,
        6,
        {(0x00000000),
         (0x5199ee9c),
         (0x0606c085),
         (0x579f2e19),
         (0xe08006b0),
         (0xb119e82c),
         (0xe686c635),
         (0xb71f28a9),
         (0x000086be),
         (0x51996822),
         (0x0606463b),
         (0x579fa8a7),
         (0xe080800e),
         (0xb1196e92),
         (0xe686408b),
         (0xb71fae17)},
        {(0x00000000),
         (0x08250000),
         (0x0a400800),
         (0x02650800),
         (0x10040000),
         (0x18210000),
         (0x1a440800),
         (0x12610800),
         (0x01081e00),
         (0x092d1e00),
         (0x0b481600),
         (0x036d1600),
         (0x110c1e00),
         (0x19291e00),
         (0x1b4c1600),
         (0x13691600)},
        {(0x3f800000),
         (0x3f841280),
         (0x3f852004),
         (0x3f813284),
         (0x3f880200),
         (0x3f8c1080),
         (0x3f8d2204),
         (0x3f893084),
         (0x3f80840f),
         (0x3f84968f),
         (0x3f85a40b),
         (0x3f81b68b),
         (0x3f88860f),
         (0x3f8c948f),
         (0x3f8da60b),
         (0x3f89b48b)},
        (0xfff80000),
        {0x29,0x35,0xcb,0x9b,0x48,0x71,0x31,0x12,0x0b,0x2e,
         0xd3,0x98,0x06,0x50,0x05,0x3b,0xac,0x89,0x1a,0xaf,0x00}
    },
    {
        /* No.108 delta:2469 weight:1251 */
        11213,
        31,
        7,
        1,
        {(0x00000000),
         (0x4efca9a0),
         (0x3a920750),
         (0x746eaef0),
         (0x958006ce),
         (0xdb7caf6e),
         (0xaf12019e),
         (0xe1eea83e),
         (0x0000ee3c),
         (0x4efc479c),
         (0x3a92e96c),
         (0x746e40cc),
         (0x9580e8f2),
         (0xdb7c4152),
         (0xaf12efa2),
         (0xe1ee4602)},
        {(0x00000000),
         (0x405c0000),
         (0x004ba200),
         (0x4017a200),
         (0x00600000),
         (0x403c0000),
         (0x002ba200),
         (0x4077a200),
         (0x80481e00),
         (0xc0141e00),
         (0x8003bc00),
         (0xc05fbc00),
         (0x80281e00),
         (0xc0741e00),
         (0x8063bc00),
         (0xc03fbc00)},
        {(0x3f800000),
         (0x3fa02e00),
         (0x3f8025d1),
         (0x3fa00bd1),
         (0x3f803000),
         (0x3fa01e00),
         (0x3f8015d1),
         (0x3fa03bd1),
         (0x3fc0240f),
         (0x3fe00a0f),
         (0x3fc001de),
         (0x3fe02fde),
         (0x3fc0140f),
         (0x3fe03a0f),
         (0x3fc031de),
         (0x3fe01fde)},
        (0xfff80000),
        {0x05,0x56,0x79,0x8d,0xa1,0x79,0x94,0x9b,0xfd,0xfa,
         0xd1,0xd9,0x42,0x04,0x4f,0x21,0x8e,0xdb,0xe7,0xa3,0x00}
    },
    {
        /* No.109 delta:997 weight:681 */
        11213,
        71,
        19,
        5,
        {(0x00000000),
         (0x21e405bb),
         (0x4f97981d),
         (0x6e739da6),
         (0x01d006d7),
         (0x2034036c),
         (0x4e479eca),
         (0x6fa39b71),
         (0x0000e6b0),
         (0x21e4e30b),
         (0x4f977ead),
         (0x6e737b16),
         (0x01d0e067),
         (0x2034e5dc),
         (0x4e47787a),
         (0x6fa37dc1)},
        {(0x00000000),
         (0x40267000),
         (0x0682d000),
         (0x46a4a000),
         (0x11832000),
         (0x51a55000),
         (0x1701f000),
         (0x57278000),
         (0x40581e00),
         (0x007e6e00),
         (0x46dace00),
         (0x06fcbe00),
         (0x51db3e00),
         (0x11fd4e00),
         (0x5759ee00),
         (0x177f9e00)},
        {(0x3f800000),
         (0x3fa01338),
         (0x3f834168),
         (0x3fa35250),
         (0x3f88c190),
         (0x3fa8d2a8),
         (0x3f8b80f8),
         (0x3fab93c0),
         (0x3fa02c0f),
         (0x3f803f37),
         (0x3fa36d67),
         (0x3f837e5f),
         (0x3fa8ed9f),
         (0x3f88fea7),
         (0x3fabacf7),
         (0x3f8bbfcf)},
        (0xfff80000),
        {0x33,0x76,0x14,0x81,0x56,0x54,0xf0,0x4f,0x12,0x90,
         0xf4,0x03,0x95,0x9c,0x1a,0x38,0x51,0x80,0x32,0xaa,0x00}
    },
    {
        /* No.110 delta:2599 weight:829 */
        11213,
        8,
        11,
        14,
        {(0x00000000),
         (0x4654c25e),
         (0xa8eaa1d6),
         (0xeebe6388),
         (0x52a006e9),
         (0x14f4c4b7),
         (0xfa4aa73f),
         (0xbc1e6561),
         (0x000019ff),
         (0x4654dba1),
         (0xa8eab829),
         (0xeebe7a77),
         (0x52a01f16),
         (0x14f4dd48),
         (0xfa4abec0),
         (0xbc1e7c9e)},
        {(0x00000000),
         (0xfe272000),
         (0x8207f000),
         (0x7c20d000),
         (0x4e344800),
         (0xb0136800),
         (0xcc33b800),
         (0x32149800),
         (0x01611e00),
         (0xff463e00),
         (0x8366ee00),
         (0x7d41ce00),
         (0x4f555600),
         (0xb1727600),
         (0xcd52a600),
         (0x33758600)},
        {(0x3f800000),
         (0x3fff1390),
         (0x3fc103f8),
         (0x3fbe1068),
         (0x3fa71a24),
         (0x3fd809b4),
         (0x3fe619dc),
         (0x3f990a4c),
         (0x3f80b08f),
         (0x3fffa31f),
         (0x3fc1b377),
         (0x3fbea0e7),
         (0x3fa7aaab),
         (0x3fd8b93b),
         (0x3fe6a953),
         (0x3f99bac3)},
        (0xfff80000),
        {0xe0,0x6d,0x93,0xcf,0x6d,0x0f,0xa4,0xf7,0x93,0x3e,
         0x67,0xb4,0x20,0xb8,0x51,0x3a,0xe3,0xcd,0xf5,0xf9,0x00}
    },
    {
        /* No.111 delta:2048 weight:1649 */
        11213,
        12,
        8,
        4,
        {(0x00000000),
         (0xbf40afc5),
         (0xdef9c9b1),
         (0x61b96674),
         (0xfee006f8),
         (0x41a0a93d),
         (0x2019cf49),
         (0x9f59608c),
         (0x000056c5),
         (0xbf40f900),
         (0xdef99f74),
         (0x61b930b1),
         (0xfee0503d),
         (0x41a0fff8),
         (0x2019998c),
         (0x9f593649)},
        {(0x00000000),
         (0x2fa10c00),
         (0x020b0000),
         (0x2daa0c00),
         (0x020ae000),
         (0x2dabec00),
         (0x0001e000),
         (0x2fa0ec00),
         (0x28021e00),
         (0x07a31200),
         (0x2a091e00),
         (0x05a81200),
         (0x2a08fe00),
         (0x05a9f200),
         (0x2803fe00),
         (0x07a2f200)},
        {(0x3f800000),
         (0x3f97d086),
         (0x3f810580),
         (0x3f96d506),
         (0x3f810570),
         (0x3f96d5f6),
         (0x3f8000f0),
         (0x3f97d076),
         (0x3f94010f),
         (0x3f83d189),
         (0x3f95048f),
         (0x3f82d409),
         (0x3f95047f),
         (0x3f82d4f9),
         (0x3f9401ff),
         (0x3f83d179)},
        (0xfff80000),
        {0x4a,0x15,0xe8,0xf1,0x59,0x83,0x8e,0x52,0x8a,0x24,
         0xa4,0xb5,0x5d,0xfb,0x87,0xe0,0x22,0xe3,0x18,0x37,0x00}
    },
    {
        /* No.112 delta:2152 weight:1369 */
        11213,
        81,
        6,
        10,
        {(0x00000000),
         (0xfbea0aeb),
         (0xba6ad14d),
         (0x4180dba6),
         (0x21e00705),
         (0xda0a0dee),
         (0x9b8ad648),
         (0x6060dca3),
         (0x00003510),
         (0xfbea3ffb),
         (0xba6ae45d),
         (0x4180eeb6),
         (0x21e03215),
         (0xda0a38fe),
         (0x9b8ae358),
         (0x6060e9b3)},
        {(0x00000000),
         (0x01428000),
         (0x04604000),
         (0x0522c000),
         (0x00081000),
         (0x014a9000),
         (0x04685000),
         (0x052ad000),
         (0x20261e00),
         (0x21649e00),
         (0x24465e00),
         (0x2504de00),
         (0x202e0e00),
         (0x216c8e00),
         (0x244e4e00),
         (0x250cce00)},
        {(0x3f800000),
         (0x3f80a140),
         (0x3f823020),
         (0x3f829160),
         (0x3f800408),
         (0x3f80a548),
         (0x3f823428),
         (0x3f829568),
         (0x3f90130f),
         (0x3f90b24f),
         (0x3f92232f),
         (0x3f92826f),
         (0x3f901707),
         (0x3f90b647),
         (0x3f922727),
         (0x3f928667)},
        (0xfff80000),
        {0xfd,0x72,0xaa,0xc7,0x20,0x44,0x28,0xe5,0x03,0x3e,
         0xf9,0x6d,0xad,0x9f,0x51,0x0c,0x87,0xf6,0x43,0x0f,0x00}
    },
    {
        /* No.113 delta:1871 weight:1215 */
        11213,
        61,
        14,
        10,
        {(0x00000000),
         (0x11856d8d),
         (0xaac84415),
         (0xbb4d2998),
         (0xae00071a),
         (0xbf856a97),
         (0x04c8430f),
         (0x154d2e82),
         (0x0000cc65),
         (0x1185a1e8),
         (0xaac88870),
         (0xbb4de5fd),
         (0xae00cb7f),
         (0xbf85a6f2),
         (0x04c88f6a),
         (0x154de2e7)},
        {(0x00000000),
         (0x006d0000),
         (0x00b10000),
         (0x00dc0000),
         (0x04490000),
         (0x04240000),
         (0x04f80000),
         (0x04950000),
         (0x00421e00),
         (0x002f1e00),
         (0x00f31e00),
         (0x009e1e00),
         (0x040b1e00),
         (0x04661e00),
         (0x04ba1e00),
         (0x04d71e00)},
        {(0x3f800000),
         (0x3f803680),
         (0x3f805880),
         (0x3f806e00),
         (0x3f822480),
         (0x3f821200),
         (0x3f827c00),
         (0x3f824a80),
         (0x3f80210f),
         (0x3f80178f),
         (0x3f80798f),
         (0x3f804f0f),
         (0x3f82058f),
         (0x3f82330f),
         (0x3f825d0f),
         (0x3f826b8f)},
        (0xfff80000),
        {0x98,0xdb,0x7c,0xec,0x90,0x27,0x75,0x69,0x6c,0x3d,
         0x4c,0xec,0xa2,0x8e,0xab,0xef,0xd1,0x0c,0xbd,0x8c,0x00}
    },
    {
        /* No.114 delta:1950 weight:1011 */
        11213,
        20,
        16,
        13,
        {(0x00000000),
         (0xbdc99be3),
         (0xaafee229),
         (0x173779ca),
         (0x7300072e),
         (0xcec99ccd),
         (0xd9fee507),
         (0x64377ee4),
         (0x0000b504),
         (0xbdc92ee7),
         (0xaafe572d),
         (0x1737ccce),
         (0x7300b22a),
         (0xcec929c9),
         (0xd9fe5003),
         (0x6437cbe0)},
        {(0x00000000),
         (0x016ec000),
         (0x8ce40000),
         (0x8d8ac000),
         (0x1c548000),
         (0x1d3a4000),
         (0x90b08000),
         (0x91de4000),
         (0x00869e00),
         (0x01e85e00),
         (0x8c629e00),
         (0x8d0c5e00),
         (0x1cd21e00),
         (0x1dbcde00),
         (0x90361e00),
         (0x9158de00)},
        {(0x3f800000),
         (0x3f80b760),
         (0x3fc67200),
         (0x3fc6c560),
         (0x3f8e2a40),
         (0x3f8e9d20),
         (0x3fc85840),
         (0x3fc8ef20),
         (0x3f80434f),
         (0x3f80f42f),
         (0x3fc6314f),
         (0x3fc6862f),
         (0x3f8e690f),
         (0x3f8ede6f),
         (0x3fc81b0f),
         (0x3fc8ac6f)},
        (0xfff80000),
        {0xa7,0x22,0x09,0x2a,0x46,0xe2,0xa3,0x13,0x26,0x8f,
         0xa5,0xc6,0x28,0x12,0x57,0xb4,0x27,0x86,0xf1,0x1d,0x00}
    },
    {
        /* No.115 delta:1732 weight:1445 */
        11213,
        29,
        17,
        7,
        {(0x00000000),
         (0xdb25076c),
         (0x9bf7e9d5),
         (0x40d2eeb9),
         (0x95f00737),
         (0x4ed5005b),
         (0x0e07eee2),
         (0xd522e98e),
         (0x0000f5f0),
         (0xdb25f29c),
         (0x9bf71c25),
         (0x40d21b49),
         (0x95f0f2c7),
         (0x4ed5f5ab),
         (0x0e071b12),
         (0xd5221c7e)},
        {(0x00000000),
         (0x481e0000),
         (0x80040000),
         (0xc81a0000),
         (0x007d0000),
         (0x48630000),
         (0x80790000),
         (0xc8670000),
         (0x40121e00),
         (0x080c1e00),
         (0xc0161e00),
         (0x88081e00),
         (0x406f1e00),
         (0x08711e00),
         (0xc06b1e00),
         (0x88751e00)},
        {(0x3f800000),
         (0x3fa40f00),
         (0x3fc00200),
         (0x3fe40d00),
         (0x3f803e80),
         (0x3fa43180),
         (0x3fc03c80),
         (0x3fe43380),
         (0x3fa0090f),
         (0x3f84060f),
         (0x3fe00b0f),
         (0x3fc4040f),
         (0x3fa0378f),
         (0x3f84388f),
         (0x3fe0358f),
         (0x3fc43a8f)},
        (0xfff80000),
        {0xc2,0xbe,0xe6,0x5f,0xf5,0x28,0x34,0x1c,0x5e,0x2d,
         0xdb,0x99,0x36,0x5d,0x04,0x52,0x9e,0x76,0x9e,0xd5,0x00}
    },
    {
        /* No.116 delta:1707 weight:1245 */
        11213,
        30,
        22,
        3,
        {(0x00000000),
         (0x0a5f3de8),
         (0xcb7a16d6),
         (0xc1252b3e),
         (0xfcd0074b),
         (0xf68f3aa3),
         (0x37aa119d),
         (0x3df52c75),
         (0x00003bff),
         (0x0a5f0617),
         (0xcb7a2d29),
         (0xc12510c1),
         (0xfcd03cb4),
         (0xf68f015c),
         (0x37aa2a62),
         (0x3df5178a)},
        {(0x00000000),
         (0x402e8e00),
         (0x0073d000),
         (0x405d5e00),
         (0x206d4000),
         (0x6043ce00),
         (0x201e9000),
         (0x60301e00),
         (0xd21dfe00),
         (0x92337000),
         (0xd26e2e00),
         (0x9240a000),
         (0xf270be00),
         (0xb25e3000),
         (0xf2036e00),
         (0xb22de000)},
        {(0x3f800000),
         (0x3fa01747),
         (0x3f8039e8),
         (0x3fa02eaf),
         (0x3f9036a0),
         (0x3fb021e7),
         (0x3f900f48),
         (0x3fb0180f),
         (0x3fe90eff),
         (0x3fc919b8),
         (0x3fe93717),
         (0x3fc92050),
         (0x3ff9385f),
         (0x3fd92f18),
         (0x3ff901b7),
         (0x3fd916f0)},
        (0xfff80000),
        {0x30,0x04,0x85,0xba,0x89,0x2d,0x19,0x39,0x57,0x1c,
         0x54,0xa8,0xb5,0x0a,0x05,0x27,0x14,0x6a,0xed,0xe0,0x00}
    },
    {
        /* No.117 delta:2917 weight:1743 */
        11213,
        37,
        3,
        6,
        {(0x00000000),
         (0x501b23d2),
         (0x2770f82a),
         (0x776bdbf8),
         (0x67300751),
         (0x372b2483),
         (0x4040ff7b),
         (0x105bdca9),
         (0x000038d9),
         (0x501b1b0b),
         (0x2770c0f3),
         (0x776be321),
         (0x67303f88),
         (0x372b1c5a),
         (0x4040c7a2),
         (0x105be470)},
        {(0x00000000),
         (0x20542200),
         (0x40387800),
         (0x606c5a00),
         (0x14402000),
         (0x34140200),
         (0x54785800),
         (0x742c7a00),
         (0x1840de00),
         (0x3814fc00),
         (0x5878a600),
         (0x782c8400),
         (0x0c00fe00),
         (0x2c54dc00),
         (0x4c388600),
         (0x6c6ca400)},
        {(0x3f800000),
         (0x3f902a11),
         (0x3fa01c3c),
         (0x3fb0362d),
         (0x3f8a2010),
         (0x3f9a0a01),
         (0x3faa3c2c),
         (0x3fba163d),
         (0x3f8c206f),
         (0x3f9c0a7e),
         (0x3fac3c53),
         (0x3fbc1642),
         (0x3f86007f),
         (0x3f962a6e),
         (0x3fa61c43),
         (0x3fb63652)},
        (0xfff80000),
        {0x86,0x60,0xf6,0x24,0x0e,0xff,0xd7,0xe9,0x32,0x8f,
         0xa0,0x96,0x77,0x67,0x0e,0xec,0x2e,0x00,0x3b,0x13,0x00}
    },
    {
        /* No.118 delta:983 weight:1587 */
        11213,
        62,
        19,
        4,
        {(0x00000000),
         (0x08eb784a),
         (0x736759ff),
         (0x7b8c21b5),
         (0x0ca0076b),
         (0x044b7f21),
         (0x7fc75e94),
         (0x772c26de),
         (0x0000d939),
         (0x08eba173),
         (0x736780c6),
         (0x7b8cf88c),
         (0x0ca0de52),
         (0x044ba618),
         (0x7fc787ad),
         (0x772cffe7)},
        {(0x00000000),
         (0x041eba00),
         (0x20637000),
         (0x247dca00),
         (0x20110000),
         (0x240fba00),
         (0x00727000),
         (0x046cca00),
         (0x10001e00),
         (0x141ea400),
         (0x30636e00),
         (0x347dd400),
         (0x30111e00),
         (0x340fa400),
         (0x10726e00),
         (0x146cd400)},
        {(0x3f800000),
         (0x3f820f5d),
         (0x3f9031b8),
         (0x3f923ee5),
         (0x3f900880),
         (0x3f9207dd),
         (0x3f803938),
         (0x3f823665),
         (0x3f88000f),
         (0x3f8a0f52),
         (0x3f9831b7),
         (0x3f9a3eea),
         (0x3f98088f),
         (0x3f9a07d2),
         (0x3f883937),
         (0x3f8a366a)},
        (0xfff80000),
        {0x70,0x7f,0x78,0xb3,0x65,0x02,0xe0,0xb9,0x55,0x49,
         0xd9,0x60,0xa8,0xa5,0xd3,0x10,0x3b,0x08,0x55,0xfd,0x00}
    },
    {
        /* No.119 delta:1397 weight:1065 */
        11213,
        57,
        14,
        11,
        {(0x00000000),
         (0x5e421ddc),
         (0x638787c4),
         (0x3dc59a18),
         (0x8e00077d),
         (0xd0421aa1),
         (0xed8780b9),
         (0xb3c59d65),
         (0x0000840f),
         (0x5e4299d3),
         (0x638703cb),
         (0x3dc51e17),
         (0x8e008372),
         (0xd0429eae),
         (0xed8704b6),
         (0xb3c5196a)},
        {(0x00000000),
         (0x08e5ec00),
         (0x245ea400),
         (0x2cbb4800),
         (0x00688000),
         (0x088d6c00),
         (0x24362400),
         (0x2cd3c800),
         (0x00111e00),
         (0x08f4f200),
         (0x244fba00),
         (0x2caa5600),
         (0x00799e00),
         (0x089c7200),
         (0x24273a00),
         (0x2cc2d600)},
        {(0x3f800000),
         (0x3f8472f6),
         (0x3f922f52),
         (0x3f965da4),
         (0x3f803440),
         (0x3f8446b6),
         (0x3f921b12),
         (0x3f9669e4),
         (0x3f80088f),
         (0x3f847a79),
         (0x3f9227dd),
         (0x3f96552b),
         (0x3f803ccf),
         (0x3f844e39),
         (0x3f92139d),
         (0x3f96616b)},
        (0xfff80000),
        {0x72,0xeb,0x0e,0x84,0xe7,0xef,0x0c,0xb3,0x72,0xaf,
         0x80,0x06,0xbc,0xf9,0x33,0xf7,0x21,0x72,0x27,0x28,0x00}
    },
    {
        /* No.120 delta:1392 weight:827 */
        11213,
        46,
        15,
        17,
        {(0x00000000),
         (0xa557bf77),
         (0x097f4c89),
         (0xac28f3fe),
         (0xffa0078b),
         (0x5af7b8fc),
         (0xf6df4b02),
         (0x5388f475),
         (0x00006249),
         (0xa557dd3e),
         (0x097f2ec0),
         (0xac2891b7),
         (0xffa065c2),
         (0x5af7dab5),
         (0xf6df294b),
         (0x5388963c)},
        {(0x00000000),
         (0x68748400),
         (0x4200ae00),
         (0x2a742a00),
         (0x1c086000),
         (0x747ce400),
         (0x5e08ce00),
         (0x367c4a00),
         (0x0046fe00),
         (0x68327a00),
         (0x42465000),
         (0x2a32d400),
         (0x1c4e9e00),
         (0x743a1a00),
         (0x5e4e3000),
         (0x363ab400)},
        {(0x3f800000),
         (0x3fb43a42),
         (0x3fa10057),
         (0x3f953a15),
         (0x3f8e0430),
         (0x3fba3e72),
         (0x3faf0467),
         (0x3f9b3e25),
         (0x3f80237f),
         (0x3fb4193d),
         (0x3fa12328),
         (0x3f95196a),
         (0x3f8e274f),
         (0x3fba1d0d),
         (0x3faf2718),
         (0x3f9b1d5a)},
        (0xfff80000),
        {0x7c,0xb2,0x6a,0xff,0x78,0xfa,0xd6,0x78,0xf9,0xba,
         0x77,0x1d,0x84,0xb3,0xbb,0xcb,0xac,0xdf,0x51,0x54,0x00}
    },
    {
        /* No.121 delta:1663 weight:775 */
        11213,
        82,
        9,
        17,
        {(0x00000000),
         (0x1fe0e7a6),
         (0x67406a7f),
         (0x78a08dd9),
         (0xda30079d),
         (0xc5d0e03b),
         (0xbd706de2),
         (0xa2908a44),
         (0x000076e4),
         (0x1fe09142),
         (0x67401c9b),
         (0x78a0fb3d),
         (0xda307179),
         (0xc5d096df),
         (0xbd701b06),
         (0xa290fca0)},
        {(0x00000000),
         (0x100e1000),
         (0x20734000),
         (0x307d5000),
         (0x5008e200),
         (0x4006f200),
         (0x707ba200),
         (0x6075b200),
         (0x3242de00),
         (0x224cce00),
         (0x12319e00),
         (0x023f8e00),
         (0x624a3c00),
         (0x72442c00),
         (0x42397c00),
         (0x52376c00)},
        {(0x3f800000),
         (0x3f880708),
         (0x3f9039a0),
         (0x3f983ea8),
         (0x3fa80471),
         (0x3fa00379),
         (0x3fb83dd1),
         (0x3fb03ad9),
         (0x3f99216f),
         (0x3f912667),
         (0x3f8918cf),
         (0x3f811fc7),
         (0x3fb1251e),
         (0x3fb92216),
         (0x3fa11cbe),
         (0x3fa91bb6)},
        (0xfff80000),
        {0xa3,0xd8,0x55,0xcd,0x98,0xdc,0x68,0x95,0x68,0xdf,
         0xcf,0x56,0x03,0x2a,0x94,0x75,0x10,0xec,0x0a,0x47,0x00}
    },
    {
        /* No.122 delta:1328 weight:751 */
        11213,
        67,
        14,
        18,
        {(0x00000000),
         (0x76bc3aa8),
         (0xc37bdc20),
         (0xb5c7e688),
         (0x38b007ab),
         (0x4e0c3d03),
         (0xfbcbdb8b),
         (0x8d77e123),
         (0x0000bd02),
         (0x76bc87aa),
         (0xc37b6122),
         (0xb5c75b8a),
         (0x38b0baa9),
         (0x4e0c8001),
         (0xfbcb6689),
         (0x8d775c21)},
        {(0x00000000),
         (0x1112c200),
         (0x18038000),
         (0x09114200),
         (0x00065000),
         (0x11149200),
         (0x1805d000),
         (0x09171200),
         (0x6001de00),
         (0x71131c00),
         (0x78025e00),
         (0x69109c00),
         (0x60078e00),
         (0x71154c00),
         (0x78040e00),
         (0x6916cc00)},
        {(0x3f800000),
         (0x3f888961),
         (0x3f8c01c0),
         (0x3f8488a1),
         (0x3f800328),
         (0x3f888a49),
         (0x3f8c02e8),
         (0x3f848b89),
         (0x3fb000ef),
         (0x3fb8898e),
         (0x3fbc012f),
         (0x3fb4884e),
         (0x3fb003c7),
         (0x3fb88aa6),
         (0x3fbc0207),
         (0x3fb48b66)},
        (0xfff80000),
        {0x18,0x30,0x32,0x35,0xe2,0xb4,0xcd,0x94,0x5e,0x6b,
         0xe6,0x78,0x0b,0x92,0x24,0xbf,0x7c,0xc5,0xbd,0xe2,0x00}
    },
    {
        /* No.123 delta:1657 weight:573 */
        11213,
        70,
        8,
        19,
        {(0x00000000),
         (0xfd59bd60),
         (0xe0041fb5),
         (0x1d5da2d5),
         (0x836007b6),
         (0x7e39bad6),
         (0x63641803),
         (0x9e3da563),
         (0x0000b1f6),
         (0xfd590c96),
         (0xe004ae43),
         (0x1d5d1323),
         (0x8360b640),
         (0x7e390b20),
         (0x6364a9f5),
         (0x9e3d1495)},
        {(0x00000000),
         (0x57b14800),
         (0x5868a400),
         (0x0fd9ec00),
         (0x084c7000),
         (0x5ffd3800),
         (0x5024d400),
         (0x07959c00),
         (0x10121e00),
         (0x47a35600),
         (0x487aba00),
         (0x1fcbf200),
         (0x185e6e00),
         (0x4fef2600),
         (0x4036ca00),
         (0x17878200)},
        {(0x3f800000),
         (0x3fabd8a4),
         (0x3fac3452),
         (0x3f87ecf6),
         (0x3f842638),
         (0x3faffe9c),
         (0x3fa8126a),
         (0x3f83cace),
         (0x3f88090f),
         (0x3fa3d1ab),
         (0x3fa43d5d),
         (0x3f8fe5f9),
         (0x3f8c2f37),
         (0x3fa7f793),
         (0x3fa01b65),
         (0x3f8bc3c1)},
        (0xfff80000),
        {0x0b,0xcb,0xeb,0x33,0x75,0x01,0x5e,0xeb,0x92,0xfb,
         0x37,0xae,0xe0,0x31,0x80,0xa9,0x06,0x42,0x2b,0xd5,0x00}
    },
    {
        /* No.124 delta:1016 weight:1635 */
        11213,
        80,
        18,
        2,
        {(0x00000000),
         (0x65b814ab),
         (0x0b7c8e4e),
         (0x6ec49ae5),
         (0xe3e007c1),
         (0x8658136a),
         (0xe89c898f),
         (0x8d249d24),
         (0x00008bf0),
         (0x65b89f5b),
         (0x0b7c05be),
         (0x6ec41115),
         (0xe3e08c31),
         (0x8658989a),
         (0xe89c027f),
         (0x8d2416d4)},
        {(0x00000000),
         (0x110f9000),
         (0x08401000),
         (0x194f8000),
         (0x20040000),
         (0x310b9000),
         (0x28441000),
         (0x394b8000),
         (0x00025e00),
         (0x110dce00),
         (0x08424e00),
         (0x194dde00),
         (0x20065e00),
         (0x3109ce00),
         (0x28464e00),
         (0x3949de00)},
        {(0x3f800000),
         (0x3f8887c8),
         (0x3f842008),
         (0x3f8ca7c0),
         (0x3f900200),
         (0x3f9885c8),
         (0x3f942208),
         (0x3f9ca5c0),
         (0x3f80012f),
         (0x3f8886e7),
         (0x3f842127),
         (0x3f8ca6ef),
         (0x3f90032f),
         (0x3f9884e7),
         (0x3f942327),
         (0x3f9ca4ef)},
        (0xfff80000),
        {0xe8,0x1c,0x9d,0xcc,0x4a,0x97,0xc0,0xae,0x9d,0xce,
         0x1a,0xa4,0x45,0x9f,0x41,0xc4,0x83,0xd9,0x62,0x72,0x00}
    },
    {
        /* No.125 delta:1214 weight:1175 */
        11213,
        28,
        16,
        7,
        {(0x00000000),
         (0x7b11b46d),
         (0x3e529f7c),
         (0x45432b11),
         (0x05a007d2),
         (0x7eb1b3bf),
         (0x3bf298ae),
         (0x40e32cc3),
         (0x00001cdb),
         (0x7b11a8b6),
         (0x3e5283a7),
         (0x454337ca),
         (0x05a01b09),
         (0x7eb1af64),
         (0x3bf28475),
         (0x40e33018)},
        {(0x00000000),
         (0x4460cc00),
         (0x50de6c00),
         (0x14bea000),
         (0x43011e00),
         (0x0761d200),
         (0x13df7200),
         (0x57bfbe00),
         (0x41807e00),
         (0x05e0b200),
         (0x115e1200),
         (0x553ede00),
         (0x02816000),
         (0x46e1ac00),
         (0x525f0c00),
         (0x163fc000)},
        {(0x3f800000),
         (0x3fa23066),
         (0x3fa86f36),
         (0x3f8a5f50),
         (0x3fa1808f),
         (0x3f83b0e9),
         (0x3f89efb9),
         (0x3fabdfdf),
         (0x3fa0c03f),
         (0x3f82f059),
         (0x3f88af09),
         (0x3faa9f6f),
         (0x3f8140b0),
         (0x3fa370d6),
         (0x3fa92f86),
         (0x3f8b1fe0)},
        (0xfff80000),
        {0x5c,0x4c,0x7f,0xad,0xc2,0x0a,0xe3,0x36,0x3a,0x3f,
         0x3e,0x28,0x97,0x26,0xce,0xab,0xdb,0x12,0x07,0x20,0x00}
    },
    {
        /* No.126 delta:962 weight:1245 */
        11213,
        78,
        18,
        5,
        {(0x00000000),
         (0xee0a807e),
         (0xe937683b),
         (0x073de845),
         (0x14f007e9),
         (0xfafa8797),
         (0xfdc76fd2),
         (0x13cdefac),
         (0x0000f676),
         (0xee0a7608),
         (0xe9379e4d),
         (0x073d1e33),
         (0x14f0f19f),
         (0xfafa71e1),
         (0xfdc799a4),
         (0x13cd19da)},
        {(0x00000000),
         (0xb028bc00),
         (0x08652400),
         (0xb84d9800),
         (0x14028600),
         (0xa42a3a00),
         (0x1c67a200),
         (0xac4f1e00),
         (0x00023e00),
         (0xb02a8200),
         (0x08671a00),
         (0xb84fa600),
         (0x1400b800),
         (0xa4280400),
         (0x1c659c00),
         (0xac4d2000)},
        {(0x3f800000),
         (0x3fd8145e),
         (0x3f843292),
         (0x3fdc26cc),
         (0x3f8a0143),
         (0x3fd2151d),
         (0x3f8e33d1),
         (0x3fd6278f),
         (0x3f80011f),
         (0x3fd81541),
         (0x3f84338d),
         (0x3fdc27d3),
         (0x3f8a005c),
         (0x3fd21402),
         (0x3f8e32ce),
         (0x3fd62690)},
        (0xfff80000),
        {0x1f,0xae,0x0b,0x7d,0x23,0x0c,0x6e,0xd6,0x26,0xa9,
         0x92,0xbc,0x8f,0x01,0x36,0x36,0x07,0xe9,0x2c,0x7c,0x00}
    },
    {
        /* No.127 delta:4656 weight:1185 */
        11213,
        57,
        4,
        13,
        {(0x00000000),
         (0x7d46d3c2),
         (0x107426fc),
         (0x6d32f53e),
         (0x832007fe),
         (0xfe66d43c),
         (0x93542102),
         (0xee12f2c0),
         (0x00009ac7),
         (0x7d464905),
         (0x1074bc3b),
         (0x6d326ff9),
         (0x83209d39),
         (0xfe664efb),
         (0x9354bbc5),
         (0xee126807)},
        {(0x00000000),
         (0x18401000),
         (0x14000000),
         (0x0c401000),
         (0x00000000),
         (0x18401000),
         (0x14000000),
         (0x0c401000),
         (0x10001e00),
         (0x08400e00),
         (0x04001e00),
         (0x1c400e00),
         (0x10001e00),
         (0x08400e00),
         (0x04001e00),
         (0x1c400e00)},
        {(0x3f800000),
         (0x3f8c2008),
         (0x3f8a0000),
         (0x3f862008),
         (0x3f800000),
         (0x3f8c2008),
         (0x3f8a0000),
         (0x3f862008),
         (0x3f88000f),
         (0x3f842007),
         (0x3f82000f),
         (0x3f8e2007),
         (0x3f88000f),
         (0x3f842007),
         (0x3f82000f),
         (0x3f8e2007)},
        (0xfff80000),
        {0x2b,0xf5,0xba,0x2c,0x06,0x65,0xd9,0xae,0x19,0x02,
         0xa4,0xb4,0xe6,0x92,0x03,0x77,0xb7,0xb8,0x8c,0x23,0x00}
    },
    {
        /* No.128 delta:2214 weight:1371 */
        11213,
        43,
        23,
        2,
        {(0x00000000),
         (0xe454d1ab),
         (0x80c8a6d9),
         (0x649c7772),
         (0xb0f00808),
         (0x54a4d9a3),
         (0x3038aed1),
         (0xd46c7f7a),
         (0x00004f07),
         (0xe4549eac),
         (0x80c8e9de),
         (0x649c3875),
         (0xb0f0470f),
         (0x54a496a4),
         (0x3038e1d6),
         (0xd46c307d)},
        {(0x00000000),
         (0xca494000),
         (0x00538000),
         (0xca1ac000),
         (0x10bd2000),
         (0xdaf46000),
         (0x10eea000),
         (0xdaa7e000),
         (0x18905e00),
         (0xd2d91e00),
         (0x18c3de00),
         (0xd28a9e00),
         (0x082d7e00),
         (0xc2643e00),
         (0x087efe00),
         (0xc237be00)},
        {(0x3f800000),
         (0x3fe524a0),
         (0x3f8029c0),
         (0x3fe50d60),
         (0x3f885e90),
         (0x3fed7a30),
         (0x3f887750),
         (0x3fed53f0),
         (0x3f8c482f),
         (0x3fe96c8f),
         (0x3f8c61ef),
         (0x3fe9454f),
         (0x3f8416bf),
         (0x3fe1321f),
         (0x3f843f7f),
         (0x3fe11bdf)},
        (0xfff80000),
        {0xf5,0xb9,0x08,0x2c,0x6d,0x40,0xb0,0x38,0xb5,0x70,
         0x83,0x86,0xbb,0x54,0xe7,0x5e,0x64,0xf3,0x40,0x61,0x00}
    },
    {
        /* No.129 delta:886 weight:915 */
        11213,
        70,
        12,
        5,
        {(0x00000000),
         (0x242c5090),
         (0x10199a60),
         (0x3435caf0),
         (0xe9d00810),
         (0xcdfc5880),
         (0xf9c99270),
         (0xdde5c2e0),
         (0x00003567),
         (0x242c65f7),
         (0x1019af07),
         (0x3435ff97),
         (0xe9d03d77),
         (0xcdfc6de7),
         (0xf9c9a717),
         (0xdde5f787)},
        {(0x00000000),
         (0x004e4000),
         (0x1009c000),
         (0x10478000),
         (0x0e025000),
         (0x0e4c1000),
         (0x1e0b9000),
         (0x1e45d000),
         (0x10345e00),
         (0x107a1e00),
         (0x003d9e00),
         (0x0073de00),
         (0x1e360e00),
         (0x1e784e00),
         (0x0e3fce00),
         (0x0e718e00)},
        {(0x3f800000),
         (0x3f802720),
         (0x3f8804e0),
         (0x3f8823c0),
         (0x3f870128),
         (0x3f872608),
         (0x3f8f05c8),
         (0x3f8f22e8),
         (0x3f881a2f),
         (0x3f883d0f),
         (0x3f801ecf),
         (0x3f8039ef),
         (0x3f8f1b07),
         (0x3f8f3c27),
         (0x3f871fe7),
         (0x3f8738c7)},
        (0xfff80000),
        {0xae,0x65,0xce,0x51,0xe8,0x63,0xb5,0x45,0x7a,0xb5,
         0x06,0x3b,0x06,0x88,0x6c,0x62,0x83,0x37,0xc8,0xb9,0x00}
    },
    {
        /* No.130 delta:2602 weight:1519 */
        11213,
        18,
        4,
        6,
        {(0x00000000),
         (0x4fa484d0),
         (0x546a6e84),
         (0x1bceea54),
         (0x4a400828),
         (0x05e48cf8),
         (0x1e2a66ac),
         (0x518ee27c),
         (0x0000bda0),
         (0x4fa43970),
         (0x546ad324),
         (0x1bce57f4),
         (0x4a40b588),
         (0x05e43158),
         (0x1e2adb0c),
         (0x518e5fdc)},
        {(0x00000000),
         (0x13081400),
         (0x928e3000),
         (0x81862400),
         (0x010e0000),
         (0x12061400),
         (0x93803000),
         (0x80882400),
         (0x10581e00),
         (0x03500a00),
         (0x82d62e00),
         (0x91de3a00),
         (0x11561e00),
         (0x025e0a00),
         (0x83d82e00),
         (0x90d03a00)},
        {(0x3f800000),
         (0x3f89840a),
         (0x3fc94718),
         (0x3fc0c312),
         (0x3f808700),
         (0x3f89030a),
         (0x3fc9c018),
         (0x3fc04412),
         (0x3f882c0f),
         (0x3f81a805),
         (0x3fc16b17),
         (0x3fc8ef1d),
         (0x3f88ab0f),
         (0x3f812f05),
         (0x3fc1ec17),
         (0x3fc8681d)},
        (0xfff80000),
        {0x13,0xcb,0x05,0x11,0x83,0xac,0xeb,0x2f,0xe5,0x15,
         0x1a,0x0e,0x87,0xa3,0x4d,0xac,0x2b,0xc0,0xb1,0xf3,0x00}
    },
    {
        /* No.131 delta:1006 weight:1303 */
        11213,
        69,
        16,
        7,
        {(0x00000000),
         (0xeb61a85a),
         (0x9e89b6f4),
         (0x75e81eae),
         (0x4cc00837),
         (0xa7a1a06d),
         (0xd249bec3),
         (0x39281699),
         (0x00006d8a),
         (0xeb61c5d0),
         (0x9e89db7e),
         (0x75e87324),
         (0x4cc065bd),
         (0xa7a1cde7),
         (0xd249d349),
         (0x39287b13)},
        {(0x00000000),
         (0x20bcb000),
         (0x51807000),
         (0x713cc000),
         (0x02a2a000),
         (0x221e1000),
         (0x5322d000),
         (0x739e6000),
         (0x00067e00),
         (0x20bace00),
         (0x51860e00),
         (0x713abe00),
         (0x02a4de00),
         (0x22186e00),
         (0x5324ae00),
         (0x73981e00)},
        {(0x3f800000),
         (0x3f905e58),
         (0x3fa8c038),
         (0x3fb89e60),
         (0x3f815150),
         (0x3f910f08),
         (0x3fa99168),
         (0x3fb9cf30),
         (0x3f80033f),
         (0x3f905d67),
         (0x3fa8c307),
         (0x3fb89d5f),
         (0x3f81526f),
         (0x3f910c37),
         (0x3fa99257),
         (0x3fb9cc0f)},
        (0xfff80000),
        {0xbc,0xf3,0x6a,0x80,0xa6,0xee,0x61,0x78,0xa8,0x3d,
         0x8c,0xee,0x29,0x1b,0x5e,0xfd,0x0b,0xf8,0x0f,0x41,0x00}
    },
    {
        /* No.132 delta:2899 weight:1651 */
        11213,
        57,
        3,
        7,
        {(0x00000000),
         (0x7a913b6d),
         (0xeed573ba),
         (0x944448d7),
         (0xfbd00846),
         (0x8141332b),
         (0x15057bfc),
         (0x6f944091),
         (0x00003675),
         (0x7a910d18),
         (0xeed545cf),
         (0x94447ea2),
         (0xfbd03e33),
         (0x8141055e),
         (0x15054d89),
         (0x6f9476e4)},
        {(0x00000000),
         (0x28410000),
         (0x92350000),
         (0xba740000),
         (0x00601000),
         (0x28211000),
         (0x92551000),
         (0xba141000),
         (0x20161e00),
         (0x08571e00),
         (0xb2231e00),
         (0x9a621e00),
         (0x20760e00),
         (0x08370e00),
         (0xb2430e00),
         (0x9a020e00)},
        {(0x3f800000),
         (0x3f942080),
         (0x3fc91a80),
         (0x3fdd3a00),
         (0x3f803008),
         (0x3f941088),
         (0x3fc92a88),
         (0x3fdd0a08),
         (0x3f900b0f),
         (0x3f842b8f),
         (0x3fd9118f),
         (0x3fcd310f),
         (0x3f903b07),
         (0x3f841b87),
         (0x3fd92187),
         (0x3fcd0107)},
        (0xfff80000),
        {0xa3,0x9f,0x64,0xc5,0x74,0x6d,0x2d,0x17,0xe7,0x4d,
         0x9a,0xaf,0xeb,0xb2,0x24,0x54,0x22,0x7a,0x93,0x8c,0x00}
    },
    {
        /* No.133 delta:1681 weight:1015 */
        11213,
        83,
        12,
        13,
        {(0x00000000),
         (0x9193fd4c),
         (0x375d8e7c),
         (0xa6ce7330),
         (0xbf90085d),
         (0x2e03f511),
         (0x88cd8621),
         (0x195e7b6d),
         (0x0000af07),
         (0x9193524b),
         (0x375d217b),
         (0xa6cedc37),
         (0xbf90a75a),
         (0x2e035a16),
         (0x88cd2926),
         (0x195ed46a)},
        {(0x00000000),
         (0x10740800),
         (0x200dc000),
         (0x3079c800),
         (0x2056f800),
         (0x3022f000),
         (0x005b3800),
         (0x102f3000),
         (0x20031e00),
         (0x30771600),
         (0x000ede00),
         (0x107ad600),
         (0x0055e600),
         (0x1021ee00),
         (0x20582600),
         (0x302c2e00)},
        {(0x3f800000),
         (0x3f883a04),
         (0x3f9006e0),
         (0x3f983ce4),
         (0x3f902b7c),
         (0x3f981178),
         (0x3f802d9c),
         (0x3f881798),
         (0x3f90018f),
         (0x3f983b8b),
         (0x3f80076f),
         (0x3f883d6b),
         (0x3f802af3),
         (0x3f8810f7),
         (0x3f902c13),
         (0x3f981617)},
        (0xfff80000),
        {0x5d,0xf8,0x9e,0x34,0xb8,0x35,0x07,0x7f,0xd2,0xe0,
         0x4f,0x6e,0x48,0xe9,0xdf,0x81,0x9d,0xf1,0x95,0x7a,0x00}
    },
    {
        /* No.134 delta:2863 weight:755 */
        11213,
        84,
        6,
        17,
        {(0x00000000),
         (0x61047d07),
         (0x957fb46d),
         (0xf47bc96a),
         (0x58f00860),
         (0x39f47567),
         (0xcd8fbc0d),
         (0xac8bc10a),
         (0x0000e9c0),
         (0x610494c7),
         (0x957f5dad),
         (0xf47b20aa),
         (0x58f0e1a0),
         (0x39f49ca7),
         (0xcd8f55cd),
         (0xac8b28ca)},
        {(0x00000000),
         (0x007c0000),
         (0x13020000),
         (0x137e0000),
         (0x00020000),
         (0x007e0000),
         (0x13000000),
         (0x137c0000),
         (0x20401e00),
         (0x203c1e00),
         (0x33421e00),
         (0x333e1e00),
         (0x20421e00),
         (0x203e1e00),
         (0x33401e00),
         (0x333c1e00)},
        {(0x3f800000),
         (0x3f803e00),
         (0x3f898100),
         (0x3f89bf00),
         (0x3f800100),
         (0x3f803f00),
         (0x3f898000),
         (0x3f89be00),
         (0x3f90200f),
         (0x3f901e0f),
         (0x3f99a10f),
         (0x3f999f0f),
         (0x3f90210f),
         (0x3f901f0f),
         (0x3f99a00f),
         (0x3f999e0f)},
        (0xfff80000),
        {0xcf,0x5c,0xa5,0xf4,0x7e,0x51,0x25,0xe8,0xf1,0x2b,
         0x6a,0x13,0xd4,0x31,0xca,0x76,0x97,0x3d,0x44,0x1a,0x00}
    },
    {
        /* No.135 delta:1967 weight:665 */
        11213,
        19,
        12,
        19,
        {(0x00000000),
         (0xabbc4cf0),
         (0xdf51c626),
         (0x74ed8ad6),
         (0x74a00878),
         (0xdf1c4488),
         (0xabf1ce5e),
         (0x004d82ae),
         (0x0000858b),
         (0xabbcc97b),
         (0xdf5143ad),
         (0x74ed0f5d),
         (0x74a08df3),
         (0xdf1cc103),
         (0xabf14bd5),
         (0x004d0725)},
        {(0x00000000),
         (0x41001800),
         (0x004bc000),
         (0x414bd800),
         (0x8b86e200),
         (0xca86fa00),
         (0x8bcd2200),
         (0xcacd3a00),
         (0x70061e00),
         (0x31060600),
         (0x704dde00),
         (0x314dc600),
         (0xfb80fc00),
         (0xba80e400),
         (0xfbcb3c00),
         (0xbacb2400)},
        {(0x3f800000),
         (0x3fa0800c),
         (0x3f8025e0),
         (0x3fa0a5ec),
         (0x3fc5c371),
         (0x3fe5437d),
         (0x3fc5e691),
         (0x3fe5669d),
         (0x3fb8030f),
         (0x3f988303),
         (0x3fb826ef),
         (0x3f98a6e3),
         (0x3ffdc07e),
         (0x3fdd4072),
         (0x3ffde59e),
         (0x3fdd6592)},
        (0xfff80000),
        {0xa2,0xe2,0xa5,0xe0,0x11,0x34,0xcb,0xf8,0xfc,0xa2,
         0x71,0xc6,0xbf,0xe2,0xc1,0x23,0x58,0x95,0xf4,0x86,0x00}
    },
    {
        /* No.136 delta:918 weight:1551 */
        11213,
        47,
        18,
        4,
        {(0x00000000),
         (0x09adadc4),
         (0xf017020b),
         (0xf9baafcf),
         (0x03300880),
         (0x0a9da544),
         (0xf3270a8b),
         (0xfa8aa74f),
         (0x0000cfe7),
         (0x09ad6223),
         (0xf017cdec),
         (0xf9ba6028),
         (0x0330c767),
         (0x0a9d6aa3),
         (0xf327c56c),
         (0xfa8a68a8)},
        {(0x00000000),
         (0x18747400),
         (0x204c5200),
         (0x38382600),
         (0x10026c00),
         (0x08761800),
         (0x304e3e00),
         (0x283a4a00),
         (0x10011e00),
         (0x08756a00),
         (0x304d4c00),
         (0x28393800),
         (0x00037200),
         (0x18770600),
         (0x204f2000),
         (0x383b5400)},
        {(0x3f800000),
         (0x3f8c3a3a),
         (0x3f902629),
         (0x3f9c1c13),
         (0x3f880136),
         (0x3f843b0c),
         (0x3f98271f),
         (0x3f941d25),
         (0x3f88008f),
         (0x3f843ab5),
         (0x3f9826a6),
         (0x3f941c9c),
         (0x3f8001b9),
         (0x3f8c3b83),
         (0x3f902790),
         (0x3f9c1daa)},
        (0xfff80000),
        {0xfd,0x25,0x81,0xf4,0x6f,0xd2,0x73,0x0c,0x1d,0xf8,
         0x3c,0x0b,0xd2,0x33,0xf7,0x54,0x12,0xcd,0x88,0xa9,0x00}
    },
    {
        /* No.137 delta:4265 weight:777 */
        11213,
        70,
        4,
        16,
        {(0x00000000),
         (0xab4eac1a),
         (0x41078fcd),
         (0xea4923d7),
         (0x9d20089e),
         (0x366ea484),
         (0xdc278753),
         (0x77692b49),
         (0x0000022a),
         (0xab4eae30),
         (0x41078de7),
         (0xea4921fd),
         (0x9d200ab4),
         (0x366ea6ae),
         (0xdc278579),
         (0x77692963)},
        {(0x00000000),
         (0x39400800),
         (0x00500000),
         (0x39100800),
         (0x002c0000),
         (0x396c0800),
         (0x007c0000),
         (0x393c0800),
         (0x5e881e00),
         (0x67c81600),
         (0x5ed81e00),
         (0x67981600),
         (0x5ea41e00),
         (0x67e41600),
         (0x5ef41e00),
         (0x67b41600)},
        {(0x3f800000),
         (0x3f9ca004),
         (0x3f802800),
         (0x3f9c8804),
         (0x3f801600),
         (0x3f9cb604),
         (0x3f803e00),
         (0x3f9c9e04),
         (0x3faf440f),
         (0x3fb3e40b),
         (0x3faf6c0f),
         (0x3fb3cc0b),
         (0x3faf520f),
         (0x3fb3f20b),
         (0x3faf7a0f),
         (0x3fb3da0b)},
        (0xfff80000),
        {0x67,0x39,0x69,0x46,0xb6,0xab,0xda,0x03,0x1a,0xb9,
         0x33,0x5c,0x43,0x1d,0xc4,0x88,0x65,0xf4,0x6e,0x7c,0x00}
    },
    {
        /* No.138 delta:2916 weight:1245 */
        11213,
        12,
        4,
        11,
        {(0x00000000),
         (0x8bd9bb70),
         (0x6536d8cf),
         (0xeeef63bf),
         (0x83c008a3),
         (0x0819b3d3),
         (0xe6f6d06c),
         (0x6d2f6b1c),
         (0x00006357),
         (0x8bd9d827),
         (0x6536bb98),
         (0xeeef00e8),
         (0x83c06bf4),
         (0x0819d084),
         (0xe6f6b33b),
         (0x6d2f084b)},
        {(0x00000000),
         (0x10b20000),
         (0x0c210800),
         (0x1c930800),
         (0x29540000),
         (0x39e60000),
         (0x25750800),
         (0x35c70800),
         (0x02001e00),
         (0x12b21e00),
         (0x0e211600),
         (0x1e931600),
         (0x2b541e00),
         (0x3be61e00),
         (0x27751600),
         (0x37c71600)},
        {(0x3f800000),
         (0x3f885900),
         (0x3f861084),
         (0x3f8e4984),
         (0x3f94aa00),
         (0x3f9cf300),
         (0x3f92ba84),
         (0x3f9ae384),
         (0x3f81000f),
         (0x3f89590f),
         (0x3f87108b),
         (0x3f8f498b),
         (0x3f95aa0f),
         (0x3f9df30f),
         (0x3f93ba8b),
         (0x3f9be38b)},
        (0xfff80000),
        {0xf3,0xfa,0x1e,0x93,0x8b,0xfb,0x73,0xc0,0xf1,0x10,
         0x33,0xac,0x5f,0x6b,0x3c,0x07,0xa2,0x98,0x75,0xed,0x00}
    },
    {
        /* No.139 delta:2344 weight:1631 */
        11213,
        19,
        8,
        7,
        {(0x00000000),
         (0xfee0d62f),
         (0x304aad76),
         (0xceaa7b59),
         (0x871008b7),
         (0x79f0de98),
         (0xb75aa5c1),
         (0x49ba73ee),
         (0x00003b80),
         (0xfee0edaf),
         (0x304a96f6),
         (0xceaa40d9),
         (0x87103337),
         (0x79f0e518),
         (0xb75a9e41),
         (0x49ba486e)},
        {(0x00000000),
         (0x05204400),
         (0x00640000),
         (0x05444400),
         (0x00510400),
         (0x05714000),
         (0x00350400),
         (0x05154000),
         (0x32861e00),
         (0x37a65a00),
         (0x32e21e00),
         (0x37c25a00),
         (0x32d71a00),
         (0x37f75e00),
         (0x32b31a00),
         (0x37935e00)},
        {(0x3f800000),
         (0x3f829022),
         (0x3f803200),
         (0x3f82a222),
         (0x3f802882),
         (0x3f82b8a0),
         (0x3f801a82),
         (0x3f828aa0),
         (0x3f99430f),
         (0x3f9bd32d),
         (0x3f99710f),
         (0x3f9be12d),
         (0x3f996b8d),
         (0x3f9bfbaf),
         (0x3f99598d),
         (0x3f9bc9af)},
        (0xfff80000),
        {0xaf,0x4b,0x20,0xda,0xcd,0xb1,0xdf,0x23,0x89,0x9b,
         0x9d,0xaf,0x0b,0x97,0x1e,0x9f,0x61,0xe7,0xfc,0x34,0x00}
    },
    {
        /* No.140 delta:8901 weight:1275 */
        11213,
        67,
        29,
        1,
        {(0x00000000),
         (0x10b888fe),
         (0xd0c6c2fc),
         (0xc07e4a02),
         (0x93a008cd),
         (0x83188033),
         (0x4366ca31),
         (0x53de42cf),
         (0x00009412),
         (0x10b81cec),
         (0xd0c656ee),
         (0xc07ede10),
         (0x93a09cdf),
         (0x83181421),
         (0x43665e23),
         (0x53ded6dd)},
        {(0x00000000),
         (0xd4620000),
         (0x7a000000),
         (0xae620000),
         (0x01000000),
         (0xd5620000),
         (0x7b000000),
         (0xaf620000),
         (0x20a01e00),
         (0xf4c21e00),
         (0x5aa01e00),
         (0x8ec21e00),
         (0x21a01e00),
         (0xf5c21e00),
         (0x5ba01e00),
         (0x8fc21e00)},
        {(0x3f800000),
         (0x3fea3100),
         (0x3fbd0000),
         (0x3fd73100),
         (0x3f808000),
         (0x3feab100),
         (0x3fbd8000),
         (0x3fd7b100),
         (0x3f90500f),
         (0x3ffa610f),
         (0x3fad500f),
         (0x3fc7610f),
         (0x3f90d00f),
         (0x3ffae10f),
         (0x3fadd00f),
         (0x3fc7e10f)},
        (0xfff80000),
        {0x57,0x96,0xba,0x7d,0x3b,0x31,0xd7,0xac,0xb2,0xe9,
         0x75,0xcd,0xdb,0x72,0x76,0x05,0x30,0x7f,0xd3,0xa0,0x00}
    },
    {
        /* No.141 delta:3341 weight:741 */
        11213,
        28,
        3,
        17,
        {(0x00000000),
         (0xac86815e),
         (0xed762180),
         (0x41f0a0de),
         (0x81a008df),
         (0x2d268981),
         (0x6cd6295f),
         (0xc050a801),
         (0x00004e54),
         (0xac86cf0a),
         (0xed766fd4),
         (0x41f0ee8a),
         (0x81a0468b),
         (0x2d26c7d5),
         (0x6cd6670b),
         (0xc050e655)},
        {(0x00000000),
         (0x9a110000),
         (0x0f440000),
         (0x95550000),
         (0x70020000),
         (0xea130000),
         (0x7f460000),
         (0xe5570000),
         (0x21561e00),
         (0xbb471e00),
         (0x2e121e00),
         (0xb4031e00),
         (0x51541e00),
         (0xcb451e00),
         (0x5e101e00),
         (0xc4011e00)},
        {(0x3f800000),
         (0x3fcd0880),
         (0x3f87a200),
         (0x3fcaaa80),
         (0x3fb80100),
         (0x3ff50980),
         (0x3fbfa300),
         (0x3ff2ab80),
         (0x3f90ab0f),
         (0x3fdda38f),
         (0x3f97090f),
         (0x3fda018f),
         (0x3fa8aa0f),
         (0x3fe5a28f),
         (0x3faf080f),
         (0x3fe2008f)},
        (0xfff80000),
        {0xcc,0x1d,0x62,0xab,0x61,0x1e,0xfc,0xe5,0x74,0x60,
         0xa2,0x5f,0xd0,0xfe,0xff,0x0e,0xfb,0x75,0x83,0x88,0x00}
    },
    {
        /* No.142 delta:5114 weight:1233 */
        11213,
        35,
        30,
        3,
        {(0x00000000),
         (0xb9cd2997),
         (0xab1ac509),
         (0x12d7ec9e),
         (0x5b2008ea),
         (0xe2ed217d),
         (0xf03acde3),
         (0x49f7e474),
         (0x0000ef9a),
         (0xb9cdc60d),
         (0xab1a2a93),
         (0x12d70304),
         (0x5b20e770),
         (0xe2edcee7),
         (0xf03a2279),
         (0x49f70bee)},
        {(0x00000000),
         (0xaef05000),
         (0x2d801000),
         (0x83704000),
         (0x2390a000),
         (0x8d60f000),
         (0x0e10b000),
         (0xa0e0e000),
         (0x01001e00),
         (0xaff04e00),
         (0x2c800e00),
         (0x82705e00),
         (0x2290be00),
         (0x8c60ee00),
         (0x0f10ae00),
         (0xa1e0fe00)},
        {(0x3f800000),
         (0x3fd77828),
         (0x3f96c008),
         (0x3fc1b820),
         (0x3f91c850),
         (0x3fc6b078),
         (0x3f870858),
         (0x3fd07070),
         (0x3f80800f),
         (0x3fd7f827),
         (0x3f964007),
         (0x3fc1382f),
         (0x3f91485f),
         (0x3fc63077),
         (0x3f878857),
         (0x3fd0f07f)},
        (0xfff80000),
        {0xce,0x51,0x4c,0x65,0x7a,0xae,0xbf,0x6f,0xd6,0x0b,
         0x0c,0x85,0x1c,0x15,0xe3,0xbf,0x9f,0x2b,0xa1,0x5d,0x00}
    },
    {
        /* No.143 delta:2115 weight:1465 */
        11213,
        11,
        20,
        4,
        {(0x00000000),
         (0x34795048),
         (0x0b9d244e),
         (0x3fe47406),
         (0xc90008f2),
         (0xfd7958ba),
         (0xc29d2cbc),
         (0xf6e47cf4),
         (0x00008680),
         (0x3479d6c8),
         (0x0b9da2ce),
         (0x3fe4f286),
         (0xc9008e72),
         (0xfd79de3a),
         (0xc29daa3c),
         (0xf6e4fa74)},
        {(0x00000000),
         (0xc1592000),
         (0x16984000),
         (0xd7c16000),
         (0x21226800),
         (0xe07b4800),
         (0x37ba2800),
         (0xf6e30800),
         (0x08345e00),
         (0xc96d7e00),
         (0x1eac1e00),
         (0xdff53e00),
         (0x29163600),
         (0xe84f1600),
         (0x3f8e7600),
         (0xfed75600)},
        {(0x3f800000),
         (0x3fe0ac90),
         (0x3f8b4c20),
         (0x3febe0b0),
         (0x3f909134),
         (0x3ff03da4),
         (0x3f9bdd14),
         (0x3ffb7184),
         (0x3f841a2f),
         (0x3fe4b6bf),
         (0x3f8f560f),
         (0x3feffa9f),
         (0x3f948b1b),
         (0x3ff4278b),
         (0x3f9fc73b),
         (0x3fff6bab)},
        (0xfff80000),
        {0x5f,0xfb,0xd7,0xc9,0x75,0x94,0x0e,0x84,0xfd,0x2b,
         0xc3,0xaf,0xdd,0x54,0xe4,0x81,0x96,0x14,0x65,0x90,0x00}
    },
    {
        /* No.144 delta:1074 weight:843 */
        11213,
        50,
        20,
        11,
        {(0x00000000),
         (0xa8afcf6f),
         (0x497c3b6e),
         (0xe1d3f401),
         (0xa0800909),
         (0x082fc666),
         (0xe9fc3267),
         (0x4153fd08),
         (0x0000ec80),
         (0xa8af23ef),
         (0x497cd7ee),
         (0xe1d31881),
         (0xa080e589),
         (0x082f2ae6),
         (0xe9fcdee7),
         (0x41531188)},
        {(0x00000000),
         (0x00081200),
         (0xd0708400),
         (0xd0789600),
         (0x70302a00),
         (0x70383800),
         (0xa040ae00),
         (0xa048bc00),
         (0x1040fe00),
         (0x1048ec00),
         (0xc0307a00),
         (0xc0386800),
         (0x6070d400),
         (0x6078c600),
         (0xb0005000),
         (0xb0084200)},
        {(0x3f800000),
         (0x3f800409),
         (0x3fe83842),
         (0x3fe83c4b),
         (0x3fb81815),
         (0x3fb81c1c),
         (0x3fd02057),
         (0x3fd0245e),
         (0x3f88207f),
         (0x3f882476),
         (0x3fe0183d),
         (0x3fe01c34),
         (0x3fb0386a),
         (0x3fb03c63),
         (0x3fd80028),
         (0x3fd80421)},
        (0xfff80000),
        {0x95,0xfe,0x49,0x06,0x5e,0xae,0xca,0x7d,0x0a,0xce,
         0xf9,0x80,0x70,0xc2,0x03,0xa9,0x1c,0xc3,0x16,0x85,0x00}
    },
    {
        /* No.145 delta:2918 weight:721 */
        11213,
        86,
        4,
        18,
        {(0x00000000),
         (0x3bebda5d),
         (0x18b70390),
         (0x235cd9cd),
         (0x2c60091c),
         (0x178bd341),
         (0x34d70a8c),
         (0x0f3cd0d1),
         (0x00004f75),
         (0x3beb9528),
         (0x18b74ce5),
         (0x235c96b8),
         (0x2c604669),
         (0x178b9c34),
         (0x34d745f9),
         (0x0f3c9fa4)},
        {(0x00000000),
         (0x1e710200),
         (0x09800000),
         (0x17f10200),
         (0x00c00000),
         (0x1eb10200),
         (0x09400000),
         (0x17310200),
         (0x30431e00),
         (0x2e321c00),
         (0x39c31e00),
         (0x27b21c00),
         (0x30831e00),
         (0x2ef21c00),
         (0x39031e00),
         (0x27721c00)},
        {(0x3f800000),
         (0x3f8f3881),
         (0x3f84c000),
         (0x3f8bf881),
         (0x3f806000),
         (0x3f8f5881),
         (0x3f84a000),
         (0x3f8b9881),
         (0x3f98218f),
         (0x3f97190e),
         (0x3f9ce18f),
         (0x3f93d90e),
         (0x3f98418f),
         (0x3f97790e),
         (0x3f9c818f),
         (0x3f93b90e)},
        (0xfff80000),
        {0x3c,0x75,0x1b,0x34,0x56,0x7d,0x5c,0xdd,0x5a,0x41,
         0xb9,0x82,0x6d,0x1d,0xc3,0x11,0x62,0x1e,0xea,0x73,0x00}
    },
    {
        /* No.146 delta:2588 weight:993 */
        11213,
        77,
        8,
        11,
        {(0x00000000),
         (0x29b76eba),
         (0xb5519d7b),
         (0x9ce6f3c1),
         (0xac000920),
         (0x85b7679a),
         (0x1951945b),
         (0x30e6fae1),
         (0x00002499),
         (0x29b74a23),
         (0xb551b9e2),
         (0x9ce6d758),
         (0xac002db9),
         (0x85b74303),
         (0x1951b0c2),
         (0x30e6de78)},
        {(0x00000000),
         (0x00350000),
         (0x086a0000),
         (0x085f0000),
         (0x085c0000),
         (0x08690000),
         (0x00360000),
         (0x00030000),
         (0x00519e00),
         (0x00649e00),
         (0x083b9e00),
         (0x080e9e00),
         (0x080d9e00),
         (0x08389e00),
         (0x00679e00),
         (0x00529e00)},
        {(0x3f800000),
         (0x3f801a80),
         (0x3f843500),
         (0x3f842f80),
         (0x3f842e00),
         (0x3f843480),
         (0x3f801b00),
         (0x3f800180),
         (0x3f8028cf),
         (0x3f80324f),
         (0x3f841dcf),
         (0x3f84074f),
         (0x3f8406cf),
         (0x3f841c4f),
         (0x3f8033cf),
         (0x3f80294f)},
        (0xfff80000),
        {0x78,0x89,0x90,0x85,0x2f,0x08,0xc7,0x62,0xed,0x99,
         0x04,0xfb,0xe5,0x26,0x8d,0x20,0x08,0xbc,0xe0,0x01,0x00}
    },
    {
        /* No.147 delta:1196 weight:1067 */
        11213,
        65,
        15,
        11,
        {(0x00000000),
         (0xe3c8118c),
         (0x346df077),
         (0xd7a5e1fb),
         (0xff500936),
         (0x1c9818ba),
         (0xcb3df941),
         (0x28f5e8cd),
         (0x000057eb),
         (0xe3c84667),
         (0x346da79c),
         (0xd7a5b610),
         (0xff505edd),
         (0x1c984f51),
         (0xcb3daeaa),
         (0x28f5bf26)},
        {(0x00000000),
         (0x00411800),
         (0x0c060000),
         (0x0c471800),
         (0x260a4800),
         (0x264b5000),
         (0x2a0c4800),
         (0x2a4d5000),
         (0x0003de00),
         (0x0042c600),
         (0x0c05de00),
         (0x0c44c600),
         (0x26099600),
         (0x26488e00),
         (0x2a0f9600),
         (0x2a4e8e00)},
        {(0x3f800000),
         (0x3f80208c),
         (0x3f860300),
         (0x3f86238c),
         (0x3f930524),
         (0x3f9325a8),
         (0x3f950624),
         (0x3f9526a8),
         (0x3f8001ef),
         (0x3f802163),
         (0x3f8602ef),
         (0x3f862263),
         (0x3f9304cb),
         (0x3f932447),
         (0x3f9507cb),
         (0x3f952747)},
        (0xfff80000),
        {0x1f,0x15,0xcd,0x92,0x4b,0xcd,0x46,0xa5,0x75,0xd5,
         0x6e,0xf4,0x6d,0x40,0x10,0x3c,0xa0,0xd0,0x56,0x85,0x00}
    },
    {
        /* No.148 delta:995 weight:1397 */
        11213,
        48,
        20,
        6,
        {(0x00000000),
         (0x81afe9a5),
         (0xc7e54dd3),
         (0x464aa476),
         (0x42100942),
         (0xc3bfe0e7),
         (0x85f54491),
         (0x045aad34),
         (0x0000d573),
         (0x81af3cd6),
         (0xc7e598a0),
         (0x464a7105),
         (0x4210dc31),
         (0xc3bf3594),
         (0x85f591e2),
         (0x045a7847)},
        {(0x00000000),
         (0x0c8d6800),
         (0x00660a00),
         (0x0ceb6200),
         (0x0190c600),
         (0x0d1dae00),
         (0x01f6cc00),
         (0x0d7ba400),
         (0x00423e00),
         (0x0ccf5600),
         (0x00243400),
         (0x0ca95c00),
         (0x01d2f800),
         (0x0d5f9000),
         (0x01b4f200),
         (0x0d399a00)},
        {(0x3f800000),
         (0x3f8646b4),
         (0x3f803305),
         (0x3f8675b1),
         (0x3f80c863),
         (0x3f868ed7),
         (0x3f80fb66),
         (0x3f86bdd2),
         (0x3f80211f),
         (0x3f8667ab),
         (0x3f80121a),
         (0x3f8654ae),
         (0x3f80e97c),
         (0x3f86afc8),
         (0x3f80da79),
         (0x3f869ccd)},
        (0xfff80000),
        {0x89,0x5d,0x4d,0xfa,0x78,0x98,0x8a,0x59,0x89,0x1d,
         0x43,0x6d,0x08,0x21,0x0f,0x15,0xef,0xea,0xed,0x4f,0x00}
    },
    {
        /* No.149 delta:1807 weight:1017 */
        11213,
        25,
        17,
        13,
        {(0x00000000),
         (0x95fafe23),
         (0x258e5d0f),
         (0xb074a32c),
         (0x5b000950),
         (0xcefaf773),
         (0x7e8e545f),
         (0xeb74aa7c),
         (0x00007b8f),
         (0x95fa85ac),
         (0x258e2680),
         (0xb074d8a3),
         (0x5b0072df),
         (0xcefa8cfc),
         (0x7e8e2fd0),
         (0xeb74d1f3)},
        {(0x00000000),
         (0x40690000),
         (0x107c4000),
         (0x50154000),
         (0x0061b000),
         (0x4008b000),
         (0x101df000),
         (0x5074f000),
         (0x10221e00),
         (0x504b1e00),
         (0x005e5e00),
         (0x40375e00),
         (0x1043ae00),
         (0x502aae00),
         (0x003fee00),
         (0x4056ee00)},
        {(0x3f800000),
         (0x3fa03480),
         (0x3f883e20),
         (0x3fa80aa0),
         (0x3f8030d8),
         (0x3fa00458),
         (0x3f880ef8),
         (0x3fa83a78),
         (0x3f88110f),
         (0x3fa8258f),
         (0x3f802f2f),
         (0x3fa01baf),
         (0x3f8821d7),
         (0x3fa81557),
         (0x3f801ff7),
         (0x3fa02b77)},
        (0xfff80000),
        {0x29,0x89,0x68,0x24,0x4a,0xc0,0x9c,0xf5,0x79,0x14,
         0xc1,0x26,0xaa,0x3d,0xbf,0x1f,0xc1,0xbc,0x78,0x97,0x00}
    },
    {
        /* No.150 delta:2642 weight:975 */
        11213,
        44,
        4,
        15,
        {(0x00000000),
         (0x3cf7745b),
         (0x8a251fd4),
         (0xb6d26b8f),
         (0xc580096c),
         (0xf9777d37),
         (0x4fa516b8),
         (0x735262e3),
         (0x00007ddf),
         (0x3cf70984),
         (0x8a25620b),
         (0xb6d21650),
         (0xc58074b3),
         (0xf97700e8),
         (0x4fa56b67),
         (0x73521f3c)},
        {(0x00000000),
         (0x52780000),
         (0x13600000),
         (0x41180000),
         (0x08cc0000),
         (0x5ab40000),
         (0x1bac0000),
         (0x49d40000),
         (0x02401e00),
         (0x50381e00),
         (0x11201e00),
         (0x43581e00),
         (0x0a8c1e00),
         (0x58f41e00),
         (0x19ec1e00),
         (0x4b941e00)},
        {(0x3f800000),
         (0x3fa93c00),
         (0x3f89b000),
         (0x3fa08c00),
         (0x3f846600),
         (0x3fad5a00),
         (0x3f8dd600),
         (0x3fa4ea00),
         (0x3f81200f),
         (0x3fa81c0f),
         (0x3f88900f),
         (0x3fa1ac0f),
         (0x3f85460f),
         (0x3fac7a0f),
         (0x3f8cf60f),
         (0x3fa5ca0f)},
        (0xfff80000),
        {0x1c,0x8b,0x81,0x2c,0x97,0xf0,0xd6,0x0f,0xeb,0x28,
         0x7e,0xbd,0x56,0xe1,0x95,0x03,0x72,0xd1,0x0a,0x96,0x00}
    },
    {
        /* No.151 delta:962 weight:1023 */
        11213,
        78,
        12,
        10,
        {(0x00000000),
         (0x171b50e0),
         (0x997dbf57),
         (0x8e66efb7),
         (0x22d00977),
         (0x35cb5997),
         (0xbbadb620),
         (0xacb6e6c0),
         (0x00001c87),
         (0x171b4c67),
         (0x997da3d0),
         (0x8e66f330),
         (0x22d015f0),
         (0x35cb4510),
         (0xbbadaaa7),
         (0xacb6fa47)},
        {(0x00000000),
         (0x21821c00),
         (0x10102200),
         (0x31923e00),
         (0x60023000),
         (0x41802c00),
         (0x70121200),
         (0x51900e00),
         (0xc100de00),
         (0xe082c200),
         (0xd110fc00),
         (0xf092e000),
         (0xa102ee00),
         (0x8080f200),
         (0xb112cc00),
         (0x9090d000)},
        {(0x3f800000),
         (0x3f90c10e),
         (0x3f880811),
         (0x3f98c91f),
         (0x3fb00118),
         (0x3fa0c016),
         (0x3fb80909),
         (0x3fa8c807),
         (0x3fe0806f),
         (0x3ff04161),
         (0x3fe8887e),
         (0x3ff84970),
         (0x3fd08177),
         (0x3fc04079),
         (0x3fd88966),
         (0x3fc84868)},
        (0xfff80000),
        {0xb1,0xe6,0xdf,0x01,0xdd,0xa2,0x07,0x31,0x50,0x37,
         0xbf,0x34,0x2a,0x6d,0xfe,0xe7,0x69,0x32,0x01,0x60,0x00}
    },
    {
        /* No.152 delta:1302 weight:1041 */
        11213,
        55,
        10,
        13,
        {(0x00000000),
         (0xd0556a8d),
         (0x5e4d31d5),
         (0x8e185b58),
         (0xe6600989),
         (0x36356304),
         (0xb82d385c),
         (0x687852d1),
         (0x000019bb),
         (0xd0557336),
         (0x5e4d286e),
         (0x8e1842e3),
         (0xe6601032),
         (0x36357abf),
         (0xb82d21e7),
         (0x68784b6a)},
        {(0x00000000),
         (0x627c4000),
         (0x3034a000),
         (0x5248e000),
         (0x00038000),
         (0x627fc000),
         (0x30372000),
         (0x524b6000),
         (0x80519e00),
         (0xe22dde00),
         (0xb0653e00),
         (0xd2197e00),
         (0x80521e00),
         (0xe22e5e00),
         (0xb066be00),
         (0xd21afe00)},
        {(0x3f800000),
         (0x3fb13e20),
         (0x3f981a50),
         (0x3fa92470),
         (0x3f8001c0),
         (0x3fb13fe0),
         (0x3f981b90),
         (0x3fa925b0),
         (0x3fc028cf),
         (0x3ff116ef),
         (0x3fd8329f),
         (0x3fe90cbf),
         (0x3fc0290f),
         (0x3ff1172f),
         (0x3fd8335f),
         (0x3fe90d7f)},
        (0xfff80000),
        {0x9e,0x26,0x73,0xd6,0xbe,0x9b,0x5f,0x7d,0x6b,0x71,
         0x07,0x55,0x14,0xdf,0x70,0x0c,0x4a,0x9a,0xdd,0x23,0x00}
    },
    {
        /* No.153 delta:1084 weight:1123 */
        11213,
        86,
        22,
        10,
        {(0x00000000),
         (0xa2a5f5b9),
         (0x2b421269),
         (0x89e7e7d0),
         (0x54300991),
         (0xf695fc28),
         (0x7f721bf8),
         (0xddd7ee41),
         (0x0000dc1c),
         (0xa2a529a5),
         (0x2b42ce75),
         (0x89e73bcc),
         (0x5430d58d),
         (0xf6952034),
         (0x7f72c7e4),
         (0xddd7325d)},
        {(0x00000000),
         (0x40cc0000),
         (0x50668000),
         (0x10aa8000),
         (0x0069f000),
         (0x40a5f000),
         (0x500f7000),
         (0x10c37000),
         (0x00131e00),
         (0x40df1e00),
         (0x50759e00),
         (0x10b99e00),
         (0x007aee00),
         (0x40b6ee00),
         (0x501c6e00),
         (0x10d06e00)},
        {(0x3f800000),
         (0x3fa06600),
         (0x3fa83340),
         (0x3f885540),
         (0x3f8034f8),
         (0x3fa052f8),
         (0x3fa807b8),
         (0x3f8861b8),
         (0x3f80098f),
         (0x3fa06f8f),
         (0x3fa83acf),
         (0x3f885ccf),
         (0x3f803d77),
         (0x3fa05b77),
         (0x3fa80e37),
         (0x3f886837)},
        (0xfff80000),
        {0x27,0x0a,0x3f,0xdb,0x36,0x65,0x8b,0x9c,0x6c,0x3d,
         0xb3,0x81,0x6b,0x82,0xdc,0xaf,0x77,0xa0,0x5a,0x41,0x00}
    },
    {
        /* No.154 delta:1218 weight:1125 */
        11213,
        33,
        17,
        9,
        {(0x00000000),
         (0xbc510269),
         (0x187fd997),
         (0xa42edbfe),
         (0xb2d009a5),
         (0x0e810bcc),
         (0xaaafd032),
         (0x16fed25b),
         (0x00008c92),
         (0xbc518efb),
         (0x187f5505),
         (0xa42e576c),
         (0xb2d08537),
         (0x0e81875e),
         (0xaaaf5ca0),
         (0x16fe5ec9)},
        {(0x00000000),
         (0x4674aa00),
         (0x20885800),
         (0x66fcf200),
         (0x010e8000),
         (0x477a2a00),
         (0x2186d800),
         (0x67f27200),
         (0x02c2be00),
         (0x44b61400),
         (0x224ae600),
         (0x643e4c00),
         (0x03cc3e00),
         (0x45b89400),
         (0x23446600),
         (0x6530cc00)},
        {(0x3f800000),
         (0x3fa33a55),
         (0x3f90442c),
         (0x3fb37e79),
         (0x3f808740),
         (0x3fa3bd15),
         (0x3f90c36c),
         (0x3fb3f939),
         (0x3f81615f),
         (0x3fa25b0a),
         (0x3f912573),
         (0x3fb21f26),
         (0x3f81e61f),
         (0x3fa2dc4a),
         (0x3f91a233),
         (0x3fb29866)},
        (0xfff80000),
        {0xcd,0xbe,0xfa,0xd2,0x8e,0x1c,0x9f,0x67,0x49,0xe4,
         0xe4,0x6c,0xb2,0xe3,0x01,0xb2,0x56,0x77,0x18,0x40,0x00}
    },
    {
        /* No.155 delta:2783 weight:955 */
        11213,
        59,
        3,
        7,
        {(0x00000000),
         (0xac2382ba),
         (0x6ff1673d),
         (0xc3d2e587),
         (0x37a009b6),
         (0x9b838b0c),
         (0x58516e8b),
         (0xf472ec31),
         (0x0000a5f6),
         (0xac23274c),
         (0x6ff1c2cb),
         (0xc3d24071),
         (0x37a0ac40),
         (0x9b832efa),
         (0x5851cb7d),
         (0xf47249c7)},
        {(0x00000000),
         (0x14228400),
         (0x00948000),
         (0x14b60400),
         (0x206a8400),
         (0x34480000),
         (0x20fe0400),
         (0x34dc8000),
         (0x40481e00),
         (0x546a9a00),
         (0x40dc9e00),
         (0x54fe1a00),
         (0x60229a00),
         (0x74001e00),
         (0x60b61a00),
         (0x74949e00)},
        {(0x3f800000),
         (0x3f8a1142),
         (0x3f804a40),
         (0x3f8a5b02),
         (0x3f903542),
         (0x3f9a2400),
         (0x3f907f02),
         (0x3f9a6e40),
         (0x3fa0240f),
         (0x3faa354d),
         (0x3fa06e4f),
         (0x3faa7f0d),
         (0x3fb0114d),
         (0x3fba000f),
         (0x3fb05b0d),
         (0x3fba4a4f)},
        (0xfff80000),
        {0x12,0x6b,0xe6,0x1a,0xef,0x03,0x6a,0x5a,0xeb,0x44,
         0xd4,0xe8,0x31,0x17,0x3d,0x9b,0xd7,0xd8,0xc8,0xd2,0x00}
    },
    {
        /* No.156 delta:2681 weight:1401 */
        11213,
        11,
        22,
        2,
        {(0x00000000),
         (0xfcaff0c3),
         (0xee10c3a9),
         (0x12bf336a),
         (0x1ea009c1),
         (0xe20ff902),
         (0xf0b0ca68),
         (0x0c1f3aab),
         (0x0000a3d9),
         (0xfcaf531a),
         (0xee106070),
         (0x12bf90b3),
         (0x1ea0aa18),
         (0xe20f5adb),
         (0xf0b069b1),
         (0x0c1f9972)},
        {(0x00000000),
         (0x8f318000),
         (0x0c949000),
         (0x83a51000),
         (0x61378000),
         (0xee060000),
         (0x6da31000),
         (0xe2929000),
         (0x4044de00),
         (0xcf755e00),
         (0x4cd04e00),
         (0xc3e1ce00),
         (0x21735e00),
         (0xae42de00),
         (0x2de7ce00),
         (0xa2d64e00)},
        {(0x3f800000),
         (0x3fc798c0),
         (0x3f864a48),
         (0x3fc1d288),
         (0x3fb09bc0),
         (0x3ff70300),
         (0x3fb6d188),
         (0x3ff14948),
         (0x3fa0226f),
         (0x3fe7baaf),
         (0x3fa66827),
         (0x3fe1f0e7),
         (0x3f90b9af),
         (0x3fd7216f),
         (0x3f96f3e7),
         (0x3fd16b27)},
        (0xfff80000),
        {0x83,0x73,0xde,0x94,0x03,0x6f,0x08,0x85,0xc8,0x3c,
         0xf2,0x9a,0xb2,0x9d,0x8f,0xfe,0x34,0x78,0xe6,0x25,0x00}
    },
    {
        /* No.157 delta:4633 weight:903 */
        11213,
        70,
        1,
        8,
        {(0x00000000),
         (0x4732d8c0),
         (0x2145def8),
         (0x66770638),
         (0x8ec009da),
         (0xc9f2d11a),
         (0xaf85d722),
         (0xe8b70fe2),
         (0x000031b0),
         (0x4732e970),
         (0x2145ef48),
         (0x66773788),
         (0x8ec0386a),
         (0xc9f2e0aa),
         (0xaf85e692),
         (0xe8b73e52)},
        {(0x00000000),
         (0x10400000),
         (0x24640000),
         (0x34240000),
         (0x81100000),
         (0x91500000),
         (0xa5740000),
         (0xb5340000),
         (0x08431e00),
         (0x18031e00),
         (0x2c271e00),
         (0x3c671e00),
         (0x89531e00),
         (0x99131e00),
         (0xad371e00),
         (0xbd771e00)},
        {(0x3f800000),
         (0x3f882000),
         (0x3f923200),
         (0x3f9a1200),
         (0x3fc08800),
         (0x3fc8a800),
         (0x3fd2ba00),
         (0x3fda9a00),
         (0x3f84218f),
         (0x3f8c018f),
         (0x3f96138f),
         (0x3f9e338f),
         (0x3fc4a98f),
         (0x3fcc898f),
         (0x3fd69b8f),
         (0x3fdebb8f)},
        (0xfff80000),
        {0xdd,0x87,0x76,0x32,0x84,0x88,0x7a,0x5f,0x52,0x09,
         0x1f,0x36,0xae,0xfa,0x7b,0x5d,0xb4,0x87,0x91,0x59,0x00}
    },
    {
        /* No.158 delta:1376 weight:811 */
        11213,
        58,
        16,
        14,
        {(0x00000000),
         (0xdc5d3a0a),
         (0x023cad01),
         (0xde61970b),
         (0x286009e2),
         (0xf43d33e8),
         (0x2a5ca4e3),
         (0xf6019ee9),
         (0x00009c42),
         (0xdc5da648),
         (0x023c3143),
         (0xde610b49),
         (0x286095a0),
         (0xf43dafaa),
         (0x2a5c38a1),
         (0xf60102ab)},
        {(0x00000000),
         (0x50081000),
         (0x305e0000),
         (0x60561000),
         (0x10324000),
         (0x403a5000),
         (0x206c4000),
         (0x70645000),
         (0x20011e00),
         (0x70090e00),
         (0x105f1e00),
         (0x40570e00),
         (0x30335e00),
         (0x603b4e00),
         (0x006d5e00),
         (0x50654e00)},
        {(0x3f800000),
         (0x3fa80408),
         (0x3f982f00),
         (0x3fb02b08),
         (0x3f881920),
         (0x3fa01d28),
         (0x3f903620),
         (0x3fb83228),
         (0x3f90008f),
         (0x3fb80487),
         (0x3f882f8f),
         (0x3fa02b87),
         (0x3f9819af),
         (0x3fb01da7),
         (0x3f8036af),
         (0x3fa832a7)},
        (0xfff80000),
        {0x96,0xe0,0xc6,0xef,0x7f,0x51,0x3c,0x13,0xd3,0x63,
         0x80,0x54,0xc8,0x09,0xcc,0x33,0x8e,0x9a,0x15,0x91,0x00}
    },
    {
        /* No.159 delta:1647 weight:1099 */
        11213,
        91,
        19,
        1,
        {(0x00000000),
         (0xeffea95b),
         (0x587a7fdf),
         (0xb784d684),
         (0xe5b009ff),
         (0x0a4ea0a4),
         (0xbdca7620),
         (0x5234df7b),
         (0x00008677),
         (0xeffe2f2c),
         (0x587af9a8),
         (0xb78450f3),
         (0xe5b08f88),
         (0x0a4e26d3),
         (0xbdcaf057),
         (0x5234590c)},
        {(0x00000000),
         (0xc3463c00),
         (0x20814400),
         (0xe3c77800),
         (0x04036400),
         (0xc7455800),
         (0x24822000),
         (0xe7c41c00),
         (0x66201e00),
         (0xa5662200),
         (0x46a15a00),
         (0x85e76600),
         (0x62237a00),
         (0xa1654600),
         (0x42a23e00),
         (0x81e40200)},
        {(0x3f800000),
         (0x3fe1a31e),
         (0x3f9040a2),
         (0x3ff1e3bc),
         (0x3f8201b2),
         (0x3fe3a2ac),
         (0x3f924110),
         (0x3ff3e20e),
         (0x3fb3100f),
         (0x3fd2b311),
         (0x3fa350ad),
         (0x3fc2f3b3),
         (0x3fb111bd),
         (0x3fd0b2a3),
         (0x3fa1511f),
         (0x3fc0f201)},
        (0xfff80000),
        {0x6f,0x70,0x62,0xec,0x14,0x82,0xd2,0xb9,0xc0,0x8d,
         0xdc,0xae,0x07,0x77,0xb8,0x94,0x3f,0xc9,0xfa,0xe1,0x00}
    },
    {
        /* No.160 delta:2967 weight:901 */
        11213,
        53,
        18,
        1,
        {(0x00000000),
         (0xaa302b40),
         (0x85ec337a),
         (0x2fdc183a),
         (0x3dd00a0a),
         (0x97e0214a),
         (0xb83c3970),
         (0x120c1230),
         (0x00008c9b),
         (0xaa30a7db),
         (0x85ecbfe1),
         (0x2fdc94a1),
         (0x3dd08691),
         (0x97e0add1),
         (0xb83cb5eb),
         (0x120c9eab)},
        {(0x00000000),
         (0x0e4c0c00),
         (0x81a28000),
         (0x8fee8c00),
         (0x01009400),
         (0x0f4c9800),
         (0x80a21400),
         (0x8eee1800),
         (0x09031e00),
         (0x074f1200),
         (0x88a19e00),
         (0x86ed9200),
         (0x08038a00),
         (0x064f8600),
         (0x89a10a00),
         (0x87ed0600)},
        {(0x3f800000),
         (0x3f872606),
         (0x3fc0d140),
         (0x3fc7f746),
         (0x3f80804a),
         (0x3f87a64c),
         (0x3fc0510a),
         (0x3fc7770c),
         (0x3f84818f),
         (0x3f83a789),
         (0x3fc450cf),
         (0x3fc376c9),
         (0x3f8401c5),
         (0x3f8327c3),
         (0x3fc4d085),
         (0x3fc3f683)},
        (0xfff80000),
        {0xa5,0x57,0xd6,0x41,0xd4,0x56,0x49,0x1c,0xe3,0x86,
         0x35,0xfc,0x5c,0xaf,0xd9,0xba,0x3e,0xfe,0x47,0xfa,0x00}
    },
    {
        /* No.161 delta:1285 weight:1009 */
        11213,
        44,
        22,
        9,
        {(0x00000000),
         (0xbd887100),
         (0x6d55a627),
         (0xd0ddd727),
         (0x18000a1d),
         (0xa5887b1d),
         (0x7555ac3a),
         (0xc8dddd3a),
         (0x0000acfa),
         (0xbd88ddfa),
         (0x6d550add),
         (0xd0dd7bdd),
         (0x1800a6e7),
         (0xa588d7e7),
         (0x755500c0),
         (0xc8dd71c0)},
        {(0x00000000),
         (0x0fe36000),
         (0x0045f000),
         (0x0fa69000),
         (0x121a2000),
         (0x1df94000),
         (0x125fd000),
         (0x1dbcb000),
         (0x00371e00),
         (0x0fd47e00),
         (0x0072ee00),
         (0x0f918e00),
         (0x122d3e00),
         (0x1dce5e00),
         (0x1268ce00),
         (0x1d8bae00)},
        {(0x3f800000),
         (0x3f87f1b0),
         (0x3f8022f8),
         (0x3f87d348),
         (0x3f890d10),
         (0x3f8efca0),
         (0x3f892fe8),
         (0x3f8ede58),
         (0x3f801b8f),
         (0x3f87ea3f),
         (0x3f803977),
         (0x3f87c8c7),
         (0x3f89169f),
         (0x3f8ee72f),
         (0x3f893467),
         (0x3f8ec5d7)},
        (0xfff80000),
        {0xb3,0x0a,0x52,0x2b,0xc3,0xf3,0x8a,0x0b,0x60,0xcf,
         0x50,0x81,0xc0,0x03,0x13,0x44,0x56,0xfd,0xe5,0x6c,0x00}
    },
    {
        /* No.162 delta:3653 weight:1321 */
        11213,
        77,
        4,
        1,
        {(0x00000000),
         (0x09eb7b50),
         (0x4a578b30),
         (0x43bcf060),
         (0xc4b00a20),
         (0xcd5b7170),
         (0x8ee78110),
         (0x870cfa40),
         (0x000030d1),
         (0x09eb4b81),
         (0x4a57bbe1),
         (0x43bcc0b1),
         (0xc4b03af1),
         (0xcd5b41a1),
         (0x8ee7b1c1),
         (0x870cca91)},
        {(0x00000000),
         (0x20630000),
         (0x00492000),
         (0x202a2000),
         (0x400a0000),
         (0x60690000),
         (0x40432000),
         (0x60202000),
         (0xc0027e00),
         (0xe0617e00),
         (0xc04b5e00),
         (0xe0285e00),
         (0x80087e00),
         (0xa06b7e00),
         (0x80415e00),
         (0xa0225e00)},
        {(0x3f800000),
         (0x3f903180),
         (0x3f802490),
         (0x3f901510),
         (0x3fa00500),
         (0x3fb03480),
         (0x3fa02190),
         (0x3fb01010),
         (0x3fe0013f),
         (0x3ff030bf),
         (0x3fe025af),
         (0x3ff0142f),
         (0x3fc0043f),
         (0x3fd035bf),
         (0x3fc020af),
         (0x3fd0112f)},
        (0xfff80000),
        {0xed,0x0f,0x9e,0x5d,0x4b,0x7d,0x2e,0x4f,0xf8,0xa4,
         0xba,0x23,0xdc,0x1a,0x0e,0x40,0xa9,0xff,0xbf,0xfa,0x00}
    },
    {
        /* No.163 delta:1515 weight:829 */
        11213,
        59,
        16,
        13,
        {(0x00000000),
         (0xd192fecc),
         (0x1c86d46e),
         (0xcd142aa2),
         (0xeb900a31),
         (0x3a02f4fd),
         (0xf716de5f),
         (0x26842093),
         (0x00009c7b),
         (0xd19262b7),
         (0x1c864815),
         (0xcd14b6d9),
         (0xeb90964a),
         (0x3a026886),
         (0xf7164224),
         (0x2684bce8)},
        {(0x00000000),
         (0xc9688000),
         (0x08400200),
         (0xc1288200),
         (0x082d4800),
         (0xc145c800),
         (0x006d4a00),
         (0xc905ca00),
         (0x312c1e00),
         (0xf8449e00),
         (0x396c1c00),
         (0xf0049c00),
         (0x39015600),
         (0xf069d600),
         (0x31415400),
         (0xf829d400)},
        {(0x3f800000),
         (0x3fe4b440),
         (0x3f842001),
         (0x3fe09441),
         (0x3f8416a4),
         (0x3fe0a2e4),
         (0x3f8036a5),
         (0x3fe482e5),
         (0x3f98960f),
         (0x3ffc224f),
         (0x3f9cb60e),
         (0x3ff8024e),
         (0x3f9c80ab),
         (0x3ff834eb),
         (0x3f98a0aa),
         (0x3ffc14ea)},
        (0xfff80000),
        {0xe7,0xaa,0x57,0x85,0x45,0x30,0x30,0xea,0x81,0x55,
         0x14,0xf3,0xe1,0x42,0x5a,0xfc,0x05,0x1e,0xd3,0xf0,0x00}
    },
    {
        /* No.164 delta:5942 weight:1403 */
        11213,
        49,
        28,
        3,
        {(0x00000000),
         (0x96bbff16),
         (0x0e2dd758),
         (0x9896284e),
         (0xb5700a46),
         (0x23cbf550),
         (0xbb5ddd1e),
         (0x2de62208),
         (0x0000f8d9),
         (0x96bb07cf),
         (0x0e2d2f81),
         (0x9896d097),
         (0xb570f29f),
         (0x23cb0d89),
         (0xbb5d25c7),
         (0x2de6dad1)},
        {(0x00000000),
         (0x44940000),
         (0x03b80000),
         (0x472c0000),
         (0x48600000),
         (0x0cf40000),
         (0x4bd80000),
         (0x0f4c0000),
         (0x00801e00),
         (0x44141e00),
         (0x03381e00),
         (0x47ac1e00),
         (0x48e01e00),
         (0x0c741e00),
         (0x4b581e00),
         (0x0fcc1e00)},
        {(0x3f800000),
         (0x3fa24a00),
         (0x3f81dc00),
         (0x3fa39600),
         (0x3fa43000),
         (0x3f867a00),
         (0x3fa5ec00),
         (0x3f87a600),
         (0x3f80400f),
         (0x3fa20a0f),
         (0x3f819c0f),
         (0x3fa3d60f),
         (0x3fa4700f),
         (0x3f863a0f),
         (0x3fa5ac0f),
         (0x3f87e60f)},
        (0xfff80000),
        {0x76,0xb3,0x89,0x96,0x29,0x62,0x6a,0x7b,0xe7,0x6f,
         0x4a,0x6c,0x1f,0xbf,0x96,0x66,0x8d,0x45,0x5d,0x5c,0x00}
    },
    {
        /* No.165 delta:1776 weight:1045 */
        11213,
        74,
        10,
        13,
        {(0x00000000),
         (0x24c994e7),
         (0x7e990c2c),
         (0x5a5098cb),
         (0xb9000a51),
         (0x9dc99eb6),
         (0xc799067d),
         (0xe350929a),
         (0x0000dbdf),
         (0x24c94f38),
         (0x7e99d7f3),
         (0x5a504314),
         (0xb900d18e),
         (0x9dc94569),
         (0xc799dda2),
         (0xe3504945)},
        {(0x00000000),
         (0x00764000),
         (0x000d4000),
         (0x007b0000),
         (0x102e6000),
         (0x10582000),
         (0x10232000),
         (0x10556000),
         (0x10065e00),
         (0x10701e00),
         (0x100b1e00),
         (0x107d5e00),
         (0x00283e00),
         (0x005e7e00),
         (0x00257e00),
         (0x00533e00)},
        {(0x3f800000),
         (0x3f803b20),
         (0x3f8006a0),
         (0x3f803d80),
         (0x3f881730),
         (0x3f882c10),
         (0x3f881190),
         (0x3f882ab0),
         (0x3f88032f),
         (0x3f88380f),
         (0x3f88058f),
         (0x3f883eaf),
         (0x3f80141f),
         (0x3f802f3f),
         (0x3f8012bf),
         (0x3f80299f)},
        (0xfff80000),
        {0xfb,0xa8,0xac,0x3b,0xfe,0x8d,0xcc,0xf9,0x64,0x79,
         0xa4,0xdb,0x6d,0x8f,0x8a,0x6e,0xb9,0x91,0x4e,0xb0,0x00}
    },
    {
        /* No.166 delta:1278 weight:1311 */
        11213,
        38,
        18,
        10,
        {(0x00000000),
         (0x1aa51933),
         (0x7654762b),
         (0x6cf16f18),
         (0x06100a69),
         (0x1cb5135a),
         (0x70447c42),
         (0x6ae16571),
         (0x0000bdcd),
         (0x1aa5a4fe),
         (0x7654cbe6),
         (0x6cf1d2d5),
         (0x0610b7a4),
         (0x1cb5ae97),
         (0x7044c18f),
         (0x6ae1d8bc)},
        {(0x00000000),
         (0x1c020c00),
         (0x10065600),
         (0x0c045a00),
         (0xa4030000),
         (0xb8010c00),
         (0xb4055600),
         (0xa8075a00),
         (0x51205e00),
         (0x4d225200),
         (0x41260800),
         (0x5d240400),
         (0xf5235e00),
         (0xe9215200),
         (0xe5250800),
         (0xf9270400)},
        {(0x3f800000),
         (0x3f8e0106),
         (0x3f88032b),
         (0x3f86022d),
         (0x3fd20180),
         (0x3fdc0086),
         (0x3fda02ab),
         (0x3fd403ad),
         (0x3fa8902f),
         (0x3fa69129),
         (0x3fa09304),
         (0x3fae9202),
         (0x3ffa91af),
         (0x3ff490a9),
         (0x3ff29284),
         (0x3ffc9382)},
        (0xfff80000),
        {0xe2,0x4b,0xe1,0x7f,0x94,0x30,0x00,0x2c,0x65,0x3e,
         0x59,0x45,0xfa,0x87,0x44,0x25,0xe1,0x2d,0xb7,0xdb,0x00}
    },
    {
        /* No.167 delta:1582 weight:743 */
        11213,
        80,
        9,
        17,
        {(0x00000000),
         (0xf8f257fd),
         (0x26c7fb3f),
         (0xde35acc2),
         (0xba900a7d),
         (0x42625d80),
         (0x9c57f142),
         (0x64a5a6bf),
         (0x00008157),
         (0xf8f2d6aa),
         (0x26c77a68),
         (0xde352d95),
         (0xba908b2a),
         (0x4262dcd7),
         (0x9c577015),
         (0x64a527e8)},
        {(0x00000000),
         (0x884f0000),
         (0x11035000),
         (0x994c5000),
         (0x2a02a800),
         (0xa24da800),
         (0x3b01f800),
         (0xb34ef800),
         (0x10041e00),
         (0x984b1e00),
         (0x01074e00),
         (0x89484e00),
         (0x3a06b600),
         (0xb249b600),
         (0x2b05e600),
         (0xa34ae600)},
        {(0x3f800000),
         (0x3fc42780),
         (0x3f8881a8),
         (0x3fcca628),
         (0x3f950154),
         (0x3fd126d4),
         (0x3f9d80fc),
         (0x3fd9a77c),
         (0x3f88020f),
         (0x3fcc258f),
         (0x3f8083a7),
         (0x3fc4a427),
         (0x3f9d035b),
         (0x3fd924db),
         (0x3f9582f3),
         (0x3fd1a573)},
        (0xfff80000),
        {0xad,0x7a,0xb9,0x26,0x28,0x43,0xfc,0x1a,0x71,0x9d,
         0xfe,0xb1,0x4c,0x75,0x45,0xcd,0x8d,0xf2,0x4d,0x01,0x00}
    },
    {
        /* No.168 delta:7619 weight:787 */
        11213,
        59,
        26,
        1,
        {(0x00000000),
         (0x98e8b6d9),
         (0x521f6a74),
         (0xcaf7dcad),
         (0x53d00a8d),
         (0xcb38bc54),
         (0x01cf60f9),
         (0x9927d620),
         (0x0000f72a),
         (0x98e841f3),
         (0x521f9d5e),
         (0xcaf72b87),
         (0x53d0fda7),
         (0xcb384b7e),
         (0x01cf97d3),
         (0x9927210a)},
        {(0x00000000),
         (0x4e200000),
         (0x70542000),
         (0x3e742000),
         (0x13942000),
         (0x5db42000),
         (0x63c00000),
         (0x2de00000),
         (0x08001e00),
         (0x46201e00),
         (0x78543e00),
         (0x36743e00),
         (0x1b943e00),
         (0x55b43e00),
         (0x6bc01e00),
         (0x25e01e00)},
        {(0x3f800000),
         (0x3fa71000),
         (0x3fb82a10),
         (0x3f9f3a10),
         (0x3f89ca10),
         (0x3faeda10),
         (0x3fb1e000),
         (0x3f96f000),
         (0x3f84000f),
         (0x3fa3100f),
         (0x3fbc2a1f),
         (0x3f9b3a1f),
         (0x3f8dca1f),
         (0x3faada1f),
         (0x3fb5e00f),
         (0x3f92f00f)},
        (0xfff80000),
        {0xc1,0xeb,0x15,0xa4,0x17,0x5a,0x94,0xa3,0xdd,0x5b,
         0x7e,0x69,0x12,0x88,0xd9,0x94,0x36,0x81,0x7c,0x98,0x00}
    },
    {
        /* No.169 delta:1710 weight:1097 */
        11213,
        57,
        14,
        11,
        {(0x00000000),
         (0x5e421ddc),
         (0x638787c4),
         (0x3dc59a18),
         (0x8e000a9d),
         (0xd0421741),
         (0xed878d59),
         (0xb3c59085),
         (0x0000840f),
         (0x5e4299d3),
         (0x638703cb),
         (0x3dc51e17),
         (0x8e008e92),
         (0xd042934e),
         (0xed870956),
         (0xb3c5148a)},
        {(0x00000000),
         (0x4a752000),
         (0xd0784000),
         (0x9a0d6000),
         (0x004c4000),
         (0x4a396000),
         (0xd0340000),
         (0x9a412000),
         (0x00005e00),
         (0x4a757e00),
         (0xd0781e00),
         (0x9a0d3e00),
         (0x004c1e00),
         (0x4a393e00),
         (0xd0345e00),
         (0x9a417e00)},
        {(0x3f800000),
         (0x3fa53a90),
         (0x3fe83c20),
         (0x3fcd06b0),
         (0x3f802620),
         (0x3fa51cb0),
         (0x3fe81a00),
         (0x3fcd2090),
         (0x3f80002f),
         (0x3fa53abf),
         (0x3fe83c0f),
         (0x3fcd069f),
         (0x3f80260f),
         (0x3fa51c9f),
         (0x3fe81a2f),
         (0x3fcd20bf)},
        (0xfff80000),
        {0x63,0xf7,0x0a,0xee,0x1d,0x41,0x31,0xa9,0xfd,0x48,
         0x35,0xb8,0x86,0xd8,0xf7,0x25,0x29,0x14,0xb0,0x12,0x00}
    },
    {
        /* No.170 delta:4372 weight:995 */
        11213,
        28,
        7,
        15,
        {(0x00000000),
         (0x655fb5a5),
         (0x87997101),
         (0xe2c6c4a4),
         (0x59f00aaa),
         (0x3cafbf0f),
         (0xde697bab),
         (0xbb36ce0e),
         (0x0000365d),
         (0x655f83f8),
         (0x8799475c),
         (0xe2c6f2f9),
         (0x59f03cf7),
         (0x3caf8952),
         (0xde694df6),
         (0xbb36f853)},
        {(0x00000000),
         (0x81898800),
         (0xd202e000),
         (0x538b6800),
         (0x08d64000),
         (0x895fc800),
         (0xdad4a000),
         (0x5b5d2800),
         (0x004a5e00),
         (0x81c3d600),
         (0xd248be00),
         (0x53c13600),
         (0x089c1e00),
         (0x89159600),
         (0xda9efe00),
         (0x5b177600)},
        {(0x3f800000),
         (0x3fc0c4c4),
         (0x3fe90170),
         (0x3fa9c5b4),
         (0x3f846b20),
         (0x3fc4afe4),
         (0x3fed6a50),
         (0x3fadae94),
         (0x3f80252f),
         (0x3fc0e1eb),
         (0x3fe9245f),
         (0x3fa9e09b),
         (0x3f844e0f),
         (0x3fc48acb),
         (0x3fed4f7f),
         (0x3fad8bbb)},
        (0xfff80000),
        {0x89,0xf5,0x02,0x8f,0x88,0xdf,0x79,0x2c,0xc5,0x67,
         0x1f,0x9e,0xd5,0x1b,0xd9,0x5a,0x79,0x7f,0xca,0x71,0x00}
    },
    {
        /* No.171 delta:3023 weight:1549 */
        11213,
        32,
        3,
        5,
        {(0x00000000),
         (0x3b134609),
         (0x5b97bf87),
         (0x6084f98e),
         (0xdbb00aba),
         (0xe0a34cb3),
         (0x8027b53d),
         (0xbb34f334),
         (0x00000ea9),
         (0x3b1348a0),
         (0x5b97b12e),
         (0x6084f727),
         (0xdbb00413),
         (0xe0a3421a),
         (0x8027bb94),
         (0xbb34fd9d)},
        {(0x00000000),
         (0x04030000),
         (0x00c10000),
         (0x04c20000),
         (0x304a8000),
         (0x34498000),
         (0x308b8000),
         (0x34888000),
         (0x00679e00),
         (0x04649e00),
         (0x00a69e00),
         (0x04a59e00),
         (0x302d1e00),
         (0x342e1e00),
         (0x30ec1e00),
         (0x34ef1e00)},
        {(0x3f800000),
         (0x3f820180),
         (0x3f806080),
         (0x3f826100),
         (0x3f982540),
         (0x3f9a24c0),
         (0x3f9845c0),
         (0x3f9a4440),
         (0x3f8033cf),
         (0x3f82324f),
         (0x3f80534f),
         (0x3f8252cf),
         (0x3f98168f),
         (0x3f9a170f),
         (0x3f98760f),
         (0x3f9a778f)},
        (0xfff80000),
        {0x6a,0x8d,0xed,0xda,0x5f,0x16,0x85,0xea,0xd9,0x4c,
         0x66,0x31,0xe2,0x9c,0xc6,0x66,0x6d,0x10,0xa0,0xf9,0x00}
    },
    {
        /* No.172 delta:892 weight:1461 */
        11213,
        41,
        18,
        4,
        {(0x00000000),
         (0x51c43b00),
         (0x7d71cf7d),
         (0x2cb5f47d),
         (0x03f00ac3),
         (0x523431c3),
         (0x7e81c5be),
         (0x2f45febe),
         (0x0000fa3d),
         (0x51c4c13d),
         (0x7d713540),
         (0x2cb50e40),
         (0x03f0f0fe),
         (0x5234cbfe),
         (0x7e813f83),
         (0x2f450483)},
        {(0x00000000),
         (0x40672000),
         (0x00508c00),
         (0x4037ac00),
         (0x00402000),
         (0x40270000),
         (0x0010ac00),
         (0x40778c00),
         (0x0e881e00),
         (0x4eef3e00),
         (0x0ed89200),
         (0x4ebfb200),
         (0x0ec83e00),
         (0x4eaf1e00),
         (0x0e98b200),
         (0x4eff9200)},
        {(0x3f800000),
         (0x3fa03390),
         (0x3f802846),
         (0x3fa01bd6),
         (0x3f802010),
         (0x3fa01380),
         (0x3f800856),
         (0x3fa03bc6),
         (0x3f87440f),
         (0x3fa7779f),
         (0x3f876c49),
         (0x3fa75fd9),
         (0x3f87641f),
         (0x3fa7578f),
         (0x3f874c59),
         (0x3fa77fc9)},
        (0xfff80000),
        {0xc5,0x69,0xaf,0xb6,0xea,0x60,0x78,0x16,0x94,0x3a,
         0x36,0x01,0x5a,0xe7,0x99,0xcd,0x09,0x6a,0xda,0xea,0x00}
    },
    {
        /* No.173 delta:1013 weight:1115 */
        11213,
        91,
        19,
        8,
        {(0x00000000),
         (0xd6aede60),
         (0xdfd2815a),
         (0x097c5f3a),
         (0xbc400ad0),
         (0x6aeed4b0),
         (0x63928b8a),
         (0xb53c55ea),
         (0x000003b3),
         (0xd6aeddd3),
         (0xdfd282e9),
         (0x097c5c89),
         (0xbc400963),
         (0x6aeed703),
         (0x63928839),
         (0xb53c5659)},
        {(0x00000000),
         (0x24590000),
         (0x08c14000),
         (0x2c984000),
         (0x80083800),
         (0xa4513800),
         (0x88c97800),
         (0xac907800),
         (0x50c09e00),
         (0x74999e00),
         (0x5801de00),
         (0x7c58de00),
         (0xd0c8a600),
         (0xf491a600),
         (0xd809e600),
         (0xfc50e600)},
        {(0x3f800000),
         (0x3f922c80),
         (0x3f8460a0),
         (0x3f964c20),
         (0x3fc0041c),
         (0x3fd2289c),
         (0x3fc464bc),
         (0x3fd6483c),
         (0x3fa8604f),
         (0x3fba4ccf),
         (0x3fac00ef),
         (0x3fbe2c6f),
         (0x3fe86453),
         (0x3ffa48d3),
         (0x3fec04f3),
         (0x3ffe2873)},
        (0xfff80000),
        {0x55,0x6c,0x5c,0x20,0xdd,0xbb,0xa3,0xad,0x56,0x67,
         0x8f,0xcf,0xc9,0xb4,0xc5,0x0f,0x66,0xf7,0xb5,0xcc,0x00}
    },
    {
        /* No.174 delta:2582 weight:887 */
        11213,
        40,
        4,
        1,
        {(0x00000000),
         (0x70c0d1b0),
         (0x83972550),
         (0xf357f4e0),
         (0xbbc00ae0),
         (0xcb00db50),
         (0x38572fb0),
         (0x4897fe00),
         (0x0000df80),
         (0x70c00e30),
         (0x8397fad0),
         (0xf3572b60),
         (0xbbc0d560),
         (0xcb0004d0),
         (0x3857f030),
         (0x48972180)},
        {(0x00000000),
         (0x00b20200),
         (0x00430000),
         (0x00f10200),
         (0x40024000),
         (0x40b04200),
         (0x40414000),
         (0x40f34200),
         (0x6401de00),
         (0x64b3dc00),
         (0x6442de00),
         (0x64f0dc00),
         (0x24039e00),
         (0x24b19c00),
         (0x24409e00),
         (0x24f29c00)},
        {(0x3f800000),
         (0x3f805901),
         (0x3f802180),
         (0x3f807881),
         (0x3fa00120),
         (0x3fa05821),
         (0x3fa020a0),
         (0x3fa079a1),
         (0x3fb200ef),
         (0x3fb259ee),
         (0x3fb2216f),
         (0x3fb2786e),
         (0x3f9201cf),
         (0x3f9258ce),
         (0x3f92204f),
         (0x3f92794e)},
        (0xfff80000),
        {0x4c,0x34,0x96,0xf1,0x70,0xe5,0x52,0xb1,0x4d,0x38,
         0xa6,0x34,0x26,0x09,0x5c,0x6c,0x92,0xe6,0x3e,0xcb,0x00}
    },
    {
        /* No.175 delta:1962 weight:1531 */
        11213,
        11,
        19,
        4,
        {(0x00000000),
         (0x91291aaa),
         (0xdc02bffd),
         (0x4d2ba557),
         (0x1fc00af5),
         (0x8ee9105f),
         (0xc3c2b508),
         (0x52ebafa2),
         (0x0000add4),
         (0x9129b77e),
         (0xdc021229),
         (0x4d2b0883),
         (0x1fc0a721),
         (0x8ee9bd8b),
         (0xc3c218dc),
         (0x52eb0276)},
        {(0x00000000),
         (0x09ff0c00),
         (0x09141600),
         (0x00eb1a00),
         (0x7510e000),
         (0x7cefec00),
         (0x7c04f600),
         (0x75fbfa00),
         (0x05509e00),
         (0x0caf9200),
         (0x0c448800),
         (0x05bb8400),
         (0x70407e00),
         (0x79bf7200),
         (0x79546800),
         (0x70ab6400)},
        {(0x3f800000),
         (0x3f84ff86),
         (0x3f848a0b),
         (0x3f80758d),
         (0x3fba8870),
         (0x3fbe77f6),
         (0x3fbe027b),
         (0x3fbafdfd),
         (0x3f82a84f),
         (0x3f8657c9),
         (0x3f862244),
         (0x3f82ddc2),
         (0x3fb8203f),
         (0x3fbcdfb9),
         (0x3fbcaa34),
         (0x3fb855b2)},
        (0xfff80000),
        {0x6f,0x40,0xbd,0xae,0x48,0xc1,0x7d,0x5b,0x99,0x1a,
         0x4e,0x57,0x61,0x39,0x1e,0x68,0xb1,0xf7,0xc0,0xaa,0x00}
    },
    {
        /* No.176 delta:2277 weight:543 */
        11213,
        33,
        7,
        19,
        {(0x00000000),
         (0x8044010b),
         (0x62414b11),
         (0xe2054a1a),
         (0xbb700b06),
         (0x3b340a0d),
         (0xd9314017),
         (0x5975411c),
         (0x000099a5),
         (0x804498ae),
         (0x6241d2b4),
         (0xe205d3bf),
         (0xbb7092a3),
         (0x3b3493a8),
         (0xd931d9b2),
         (0x5975d8b9)},
        {(0x00000000),
         (0x0c3e0800),
         (0x80be4000),
         (0x8c804800),
         (0x00483000),
         (0x0c763800),
         (0x80f67000),
         (0x8cc87800),
         (0x00791e00),
         (0x0c471600),
         (0x80c75e00),
         (0x8cf95600),
         (0x00312e00),
         (0x0c0f2600),
         (0x808f6e00),
         (0x8cb16600)},
        {(0x3f800000),
         (0x3f861f04),
         (0x3fc05f20),
         (0x3fc64024),
         (0x3f802418),
         (0x3f863b1c),
         (0x3fc07b38),
         (0x3fc6643c),
         (0x3f803c8f),
         (0x3f86238b),
         (0x3fc063af),
         (0x3fc67cab),
         (0x3f801897),
         (0x3f860793),
         (0x3fc047b7),
         (0x3fc658b3)},
        (0xfff80000),
        {0xfc,0x12,0x23,0x4e,0x03,0xa3,0xf6,0xfd,0x28,0xb5,
         0xf1,0x0e,0x0a,0x5d,0xed,0x4a,0xd6,0x0c,0xfa,0x9a,0x00}
    },
    {
        /* No.177 delta:1331 weight:1179 */
        11213,
        60,
        21,
        4,
        {(0x00000000),
         (0x8e46be68),
         (0x9cb3a5d7),
         (0x12f51bbf),
         (0x3e600b12),
         (0xb026b57a),
         (0xa2d3aec5),
         (0x2c9510ad),
         (0x0000942c),
         (0x8e462a44),
         (0x9cb331fb),
         (0x12f58f93),
         (0x3e609f3e),
         (0xb0262156),
         (0xa2d33ae9),
         (0x2c958481)},
        {(0x00000000),
         (0x0940a800),
         (0x388c9000),
         (0x31cc3800),
         (0x00608000),
         (0x09202800),
         (0x38ec1000),
         (0x31acb800),
         (0x005c7e00),
         (0x091cd600),
         (0x38d0ee00),
         (0x31904600),
         (0x003cfe00),
         (0x097c5600),
         (0x38b06e00),
         (0x31f0c600)},
        {(0x3f800000),
         (0x3f84a054),
         (0x3f9c4648),
         (0x3f98e61c),
         (0x3f803040),
         (0x3f849014),
         (0x3f9c7608),
         (0x3f98d65c),
         (0x3f802e3f),
         (0x3f848e6b),
         (0x3f9c6877),
         (0x3f98c823),
         (0x3f801e7f),
         (0x3f84be2b),
         (0x3f9c5837),
         (0x3f98f863)},
        (0xfff80000),
        {0x71,0x3a,0xb0,0x71,0xe1,0x95,0x3c,0xba,0x23,0x00,
         0x7a,0xc6,0x93,0x4a,0xb7,0xb8,0xeb,0x00,0x24,0x8e,0x00}
    },
    {
        /* No.178 delta:3213 weight:657 */
        11213,
        46,
        22,
        1,
        {(0x00000000),
         (0x53fdf1b0),
         (0x32502578),
         (0x61add4c8),
         (0x46c00b28),
         (0x153dfa98),
         (0x74902e50),
         (0x276ddfe0),
         (0x00006d70),
         (0x53fd9cc0),
         (0x32504808),
         (0x61adb9b8),
         (0x46c06658),
         (0x153d97e8),
         (0x74904320),
         (0x276db290)},
        {(0x00000000),
         (0x44633a00),
         (0x44038600),
         (0x0060bc00),
         (0x1819c200),
         (0x5c7af800),
         (0x5c1a4400),
         (0x18797e00),
         (0x21eade00),
         (0x6589e400),
         (0x65e95800),
         (0x218a6200),
         (0x39f31c00),
         (0x7d902600),
         (0x7df09a00),
         (0x3993a000)},
        {(0x3f800000),
         (0x3fa2319d),
         (0x3fa201c3),
         (0x3f80305e),
         (0x3f8c0ce1),
         (0x3fae3d7c),
         (0x3fae0d22),
         (0x3f8c3cbf),
         (0x3f90f56f),
         (0x3fb2c4f2),
         (0x3fb2f4ac),
         (0x3f90c531),
         (0x3f9cf98e),
         (0x3fbec813),
         (0x3fbef84d),
         (0x3f9cc9d0)},
        (0xfff80000),
        {0x92,0xb2,0x66,0x1e,0x08,0xb8,0x0b,0x02,0x41,0xdc,
         0x45,0x04,0x59,0x65,0x0b,0x55,0x82,0x05,0xeb,0x6d,0x00}
    },
    {
        /* No.179 delta:4972 weight:1077 */
        11213,
        16,
        7,
        11,
        {(0x00000000),
         (0xcf8962a9),
         (0xb6d797a1),
         (0x795ef508),
         (0xfbb00b36),
         (0x3439699f),
         (0x4d679c97),
         (0x82eefe3e),
         (0x0000d59e),
         (0xcf89b737),
         (0xb6d7423f),
         (0x795e2096),
         (0xfbb0dea8),
         (0x3439bc01),
         (0x4d674909),
         (0x82ee2ba0)},
        {(0x00000000),
         (0x01000000),
         (0x08000000),
         (0x09000000),
         (0x41800000),
         (0x40800000),
         (0x49800000),
         (0x48800000),
         (0x06801e00),
         (0x07801e00),
         (0x0e801e00),
         (0x0f801e00),
         (0x47001e00),
         (0x46001e00),
         (0x4f001e00),
         (0x4e001e00)},
        {(0x3f800000),
         (0x3f808000),
         (0x3f840000),
         (0x3f848000),
         (0x3fa0c000),
         (0x3fa04000),
         (0x3fa4c000),
         (0x3fa44000),
         (0x3f83400f),
         (0x3f83c00f),
         (0x3f87400f),
         (0x3f87c00f),
         (0x3fa3800f),
         (0x3fa3000f),
         (0x3fa7800f),
         (0x3fa7000f)},
        (0xfff80000),
        {0x55,0xe1,0xc9,0x26,0x74,0x2c,0xed,0x5c,0x7b,0x89,
         0xe7,0x1d,0x6f,0x5d,0x02,0xe5,0x4a,0x19,0x8c,0x04,0x00}
    },
    {
        /* No.180 delta:2285 weight:1529 */
        11213,
        16,
        5,
        9,
        {(0x00000000),
         (0x5766ef3b),
         (0xaca2499c),
         (0xfbc4a6a7),
         (0x63800b4f),
         (0x34e6e474),
         (0xcf2242d3),
         (0x9844ade8),
         (0x0000a19a),
         (0x57664ea1),
         (0xaca2e806),
         (0xfbc4073d),
         (0x6380aad5),
         (0x34e645ee),
         (0xcf22e349),
         (0x98440c72)},
        {(0x00000000),
         (0x1901c200),
         (0x88118000),
         (0x91104200),
         (0x02ef8000),
         (0x1bee4200),
         (0x8afe0000),
         (0x93ffc200),
         (0x3087be00),
         (0x29867c00),
         (0xb8963e00),
         (0xa197fc00),
         (0x32683e00),
         (0x2b69fc00),
         (0xba79be00),
         (0xa3787c00)},
        {(0x3f800000),
         (0x3f8c80e1),
         (0x3fc408c0),
         (0x3fc88821),
         (0x3f8177c0),
         (0x3f8df721),
         (0x3fc57f00),
         (0x3fc9ffe1),
         (0x3f9843df),
         (0x3f94c33e),
         (0x3fdc4b1f),
         (0x3fd0cbfe),
         (0x3f99341f),
         (0x3f95b4fe),
         (0x3fdd3cdf),
         (0x3fd1bc3e)},
        (0xfff80000),
        {0x4b,0xb0,0xb5,0xae,0x6b,0x5e,0xea,0x44,0x3b,0xd2,
         0xcf,0xf3,0x8a,0x8d,0xff,0x98,0xeb,0xce,0xa2,0x22,0x00}
    },
    {
        /* No.181 delta:1320 weight:1461 */
        11213,
        52,
        21,
        2,
        {(0x00000000),
         (0x2f3716e3),
         (0x0af6f7a8),
         (0x25c1e14b),
         (0x67900b5a),
         (0x48a71db9),
         (0x6d66fcf2),
         (0x4251ea11),
         (0x0000adb5),
         (0x2f37bb56),
         (0x0af65a1d),
         (0x25c14cfe),
         (0x6790a6ef),
         (0x48a7b00c),
         (0x6d665147),
         (0x425147a4)},
        {(0x00000000),
         (0xc0295e00),
         (0x1040c000),
         (0xd0699e00),
         (0x20fc7200),
         (0xe0d52c00),
         (0x30bcb200),
         (0xf095ec00),
         (0x10a2fe00),
         (0xd08ba000),
         (0x00e23e00),
         (0xc0cb6000),
         (0x305e8c00),
         (0xf077d200),
         (0x201e4c00),
         (0xe0371200)},
        {(0x3f800000),
         (0x3fe014af),
         (0x3f882060),
         (0x3fe834cf),
         (0x3f907e39),
         (0x3ff06a96),
         (0x3f985e59),
         (0x3ff84af6),
         (0x3f88517f),
         (0x3fe845d0),
         (0x3f80711f),
         (0x3fe065b0),
         (0x3f982f46),
         (0x3ff83be9),
         (0x3f900f26),
         (0x3ff01b89)},
        (0xfff80000),
        {0xf7,0x30,0xe4,0xa1,0xb6,0x2c,0xff,0x05,0x23,0x13,
         0xd1,0x56,0xd7,0x7a,0x22,0x24,0xae,0xf7,0x34,0x52,0x00}
    },
    {
        /* No.182 delta:789 weight:1509 */
        11213,
        74,
        16,
        6,
        {(0x00000000),
         (0xe416060b),
         (0xd6bfa624),
         (0x32a9a02f),
         (0x56500b61),
         (0xb2460d6a),
         (0x80efad45),
         (0x64f9ab4e),
         (0x0000ce80),
         (0xe416c88b),
         (0xd6bf68a4),
         (0x32a96eaf),
         (0x5650c5e1),
         (0xb246c3ea),
         (0x80ef63c5),
         (0x64f965ce)},
        {(0x00000000),
         (0x804c0800),
         (0x107ad000),
         (0x9036d800),
         (0x0055c400),
         (0x8019cc00),
         (0x102f1400),
         (0x90631c00),
         (0x00033e00),
         (0x804f3600),
         (0x1079ee00),
         (0x9035e600),
         (0x0056fa00),
         (0x801af200),
         (0x102c2a00),
         (0x90602200)},
        {(0x3f800000),
         (0x3fc02604),
         (0x3f883d68),
         (0x3fc81b6c),
         (0x3f802ae2),
         (0x3fc00ce6),
         (0x3f88178a),
         (0x3fc8318e),
         (0x3f80019f),
         (0x3fc0279b),
         (0x3f883cf7),
         (0x3fc81af3),
         (0x3f802b7d),
         (0x3fc00d79),
         (0x3f881615),
         (0x3fc83011)},
        (0xfff80000),
        {0xa2,0xea,0x48,0x1e,0xb3,0x8f,0x99,0x75,0xba,0x12,
         0x9c,0xbf,0xf3,0xa0,0xb8,0x5d,0xc8,0x38,0xc4,0x74,0x00}
    },
    {
        /* No.183 delta:1268 weight:1109 */
        11213,
        42,
        13,
        11,
        {(0x00000000),
         (0xf7ba01fd),
         (0xaa8773ac),
         (0x5d3d7251),
         (0xa5b00b7d),
         (0x520a0a80),
         (0x0f3778d1),
         (0xf88d792c),
         (0x0000aaa0),
         (0xf7baab5d),
         (0xaa87d90c),
         (0x5d3dd8f1),
         (0xa5b0a1dd),
         (0x520aa020),
         (0x0f37d271),
         (0xf88dd38c)},
        {(0x00000000),
         (0x92abc000),
         (0x00443800),
         (0x92eff800),
         (0x00c12000),
         (0x926ae000),
         (0x00851800),
         (0x922ed800),
         (0x02a3be00),
         (0x90087e00),
         (0x02e78600),
         (0x904c4600),
         (0x02629e00),
         (0x90c95e00),
         (0x0226a600),
         (0x908d6600)},
        {(0x3f800000),
         (0x3fc955e0),
         (0x3f80221c),
         (0x3fc977fc),
         (0x3f806090),
         (0x3fc93570),
         (0x3f80428c),
         (0x3fc9176c),
         (0x3f8151df),
         (0x3fc8043f),
         (0x3f8173c3),
         (0x3fc82623),
         (0x3f81314f),
         (0x3fc864af),
         (0x3f811353),
         (0x3fc846b3)},
        (0xfff80000),
        {0xb0,0x87,0xce,0x22,0xcb,0xa4,0x10,0xdc,0xdb,0x3e,
         0x3c,0x67,0xae,0x3f,0xc0,0x11,0xf3,0xec,0xe2,0x06,0x00}
    },
    {
        /* No.184 delta:1092 weight:1295 */
        11213,
        91,
        17,
        8,
        {(0x00000000),
         (0x2fa2608e),
         (0x8f680761),
         (0xa0ca67ef),
         (0x13a00b8a),
         (0x3c026b04),
         (0x9cc80ceb),
         (0xb36a6c65),
         (0x00007f70),
         (0x2fa21ffe),
         (0x8f687811),
         (0xa0ca189f),
         (0x13a074fa),
         (0x3c021474),
         (0x9cc8739b),
         (0xb36a1315)},
        {(0x00000000),
         (0x089d6000),
         (0x1a00e200),
         (0x129d8200),
         (0x40df4000),
         (0x48422000),
         (0x5adfa200),
         (0x5242c200),
         (0x01a6de00),
         (0x093bbe00),
         (0x1ba63c00),
         (0x133b5c00),
         (0x41799e00),
         (0x49e4fe00),
         (0x5b797c00),
         (0x53e41c00)},
        {(0x3f800000),
         (0x3f844eb0),
         (0x3f8d0071),
         (0x3f894ec1),
         (0x3fa06fa0),
         (0x3fa42110),
         (0x3fad6fd1),
         (0x3fa92161),
         (0x3f80d36f),
         (0x3f849ddf),
         (0x3f8dd31e),
         (0x3f899dae),
         (0x3fa0bccf),
         (0x3fa4f27f),
         (0x3fadbcbe),
         (0x3fa9f20e)},
        (0xfff80000),
        {0xbd,0xb1,0x02,0x3f,0x46,0x65,0xe7,0x62,0xbf,0x9e,
         0x12,0xaa,0x33,0xbe,0x0c,0x5d,0x89,0x02,0x2c,0x11,0x00}
    },
    {
        /* No.185 delta:8146 weight:1071 */
        11213,
        66,
        28,
        1,
        {(0x00000000),
         (0xc3e3a60c),
         (0x80c000cd),
         (0x4323a6c1),
         (0xda800b97),
         (0x1963ad9b),
         (0x5a400b5a),
         (0x99a3ad56),
         (0x000058ad),
         (0xc3e3fea1),
         (0x80c05860),
         (0x4323fe6c),
         (0xda80533a),
         (0x1963f536),
         (0x5a4053f7),
         (0x99a3f5fb)},
        {(0x00000000),
         (0x89d10a00),
         (0x403c0200),
         (0xc9ed0800),
         (0x53520000),
         (0xda830a00),
         (0x136e0200),
         (0x9abf0800),
         (0x61609e00),
         (0xe8b19400),
         (0x215c9c00),
         (0xa88d9600),
         (0x32329e00),
         (0xbbe39400),
         (0x720e9c00),
         (0xfbdf9600)},
        {(0x3f800000),
         (0x3fc4e885),
         (0x3fa01e01),
         (0x3fe4f684),
         (0x3fa9a900),
         (0x3fed4185),
         (0x3f89b701),
         (0x3fcd5f84),
         (0x3fb0b04f),
         (0x3ff458ca),
         (0x3f90ae4e),
         (0x3fd446cb),
         (0x3f99194f),
         (0x3fddf1ca),
         (0x3fb9074e),
         (0x3ffdefcb)},
        (0xfff80000),
        {0xf7,0x3c,0x9c,0x40,0x28,0xb5,0xbe,0xbf,0x2e,0x97,
         0xa1,0x61,0x3b,0x82,0x2a,0x82,0x3c,0xa0,0xf4,0x90,0x00}
    },
    {
        /* No.186 delta:2697 weight:1957 */
        11213,
        43,
        3,
        3,
        {(0x00000000),
         (0x7a0a48fc),
         (0x29a53edd),
         (0x53af7621),
         (0x08f00ba0),
         (0x72fa435c),
         (0x2155357d),
         (0x5b5f7d81),
         (0x0000132f),
         (0x7a0a5bd3),
         (0x29a52df2),
         (0x53af650e),
         (0x08f0188f),
         (0x72fa5073),
         (0x21552652),
         (0x5b5f6eae)},
        {(0x00000000),
         (0xa2021000),
         (0x1c21a000),
         (0xbe23b000),
         (0x00408000),
         (0xa2429000),
         (0x1c612000),
         (0xbe633000),
         (0x40201e00),
         (0xe2220e00),
         (0x5c01be00),
         (0xfe03ae00),
         (0x40609e00),
         (0xe2628e00),
         (0x5c413e00),
         (0xfe432e00)},
        {(0x3f800000),
         (0x3fd10108),
         (0x3f8e10d0),
         (0x3fdf11d8),
         (0x3f802040),
         (0x3fd12148),
         (0x3f8e3090),
         (0x3fdf3198),
         (0x3fa0100f),
         (0x3ff11107),
         (0x3fae00df),
         (0x3fff01d7),
         (0x3fa0304f),
         (0x3ff13147),
         (0x3fae209f),
         (0x3fff2197)},
        (0xfff80000),
        {0x1a,0x79,0x8f,0xb2,0xec,0xc2,0xee,0xfc,0x66,0xa6,
         0x37,0x12,0x4d,0x78,0x6e,0xa1,0xb0,0x60,0x86,0x37,0x00}
    },
    {
        /* No.187 delta:2420 weight:1355 */
        11213,
        28,
        24,
        3,
        {(0x00000000),
         (0x9580742c),
         (0xce45a063),
         (0x5bc5d44f),
         (0x33f00bbf),
         (0xa6707f93),
         (0xfdb5abdc),
         (0x6835dff0),
         (0x000009ba),
         (0x95807d96),
         (0xce45a9d9),
         (0x5bc5ddf5),
         (0x33f00205),
         (0xa6707629),
         (0xfdb5a266),
         (0x6835d64a)},
        {(0x00000000),
         (0x40bf0200),
         (0x00440200),
         (0x40fb0000),
         (0x3411d000),
         (0x74aed200),
         (0x3455d200),
         (0x74ead000),
         (0x9082de00),
         (0xd03ddc00),
         (0x90c6dc00),
         (0xd079de00),
         (0xa4930e00),
         (0xe42c0c00),
         (0xa4d70c00),
         (0xe4680e00)},
        {(0x3f800000),
         (0x3fa05f81),
         (0x3f802201),
         (0x3fa07d80),
         (0x3f9a08e8),
         (0x3fba5769),
         (0x3f9a2ae9),
         (0x3fba7568),
         (0x3fc8416f),
         (0x3fe81eee),
         (0x3fc8636e),
         (0x3fe83cef),
         (0x3fd24987),
         (0x3ff21606),
         (0x3fd26b86),
         (0x3ff23407)},
        (0xfff80000),
        {0x20,0xe4,0x7d,0x6c,0xc4,0xf9,0x2b,0x4a,0x56,0x18,
         0x64,0xa0,0x86,0x2c,0x45,0x4a,0x42,0x3a,0x22,0x49,0x00}
    },
    {
        /* No.188 delta:1841 weight:735 */
        11213,
        62,
        10,
        18,
        {(0x00000000),
         (0x179a1ef2),
         (0x535f148e),
         (0x44c50a7c),
         (0x33700bc3),
         (0x24ea1531),
         (0x602f1f4d),
         (0x77b501bf),
         (0x0000df3e),
         (0x179ac1cc),
         (0x535fcbb0),
         (0x44c5d542),
         (0x3370d4fd),
         (0x24eaca0f),
         (0x602fc073),
         (0x77b5de81)},
        {(0x00000000),
         (0x30b90000),
         (0x083c0000),
         (0x38850000),
         (0x40070000),
         (0x70be0000),
         (0x483b0000),
         (0x78820000),
         (0x10651e00),
         (0x20dc1e00),
         (0x18591e00),
         (0x28e01e00),
         (0x50621e00),
         (0x60db1e00),
         (0x585e1e00),
         (0x68e71e00)},
        {(0x3f800000),
         (0x3f985c80),
         (0x3f841e00),
         (0x3f9c4280),
         (0x3fa00380),
         (0x3fb85f00),
         (0x3fa41d80),
         (0x3fbc4100),
         (0x3f88328f),
         (0x3f906e0f),
         (0x3f8c2c8f),
         (0x3f94700f),
         (0x3fa8310f),
         (0x3fb06d8f),
         (0x3fac2f0f),
         (0x3fb4738f)},
        (0xfff80000),
        {0x09,0x19,0x78,0xe9,0x81,0xea,0xdf,0x95,0x46,0x6b,
         0x4f,0xa1,0xca,0x2b,0xbc,0x8a,0x0b,0xaa,0x44,0xb8,0x00}
    },
    {
        /* No.189 delta:3318 weight:1469 */
        11213,
        81,
        3,
        7,
        {(0x00000000),
         (0xfe728280),
         (0xcc048331),
         (0x327601b1),
         (0x42200bd8),
         (0xbc528958),
         (0x8e2488e9),
         (0x70560a69),
         (0x0000d1a0),
         (0xfe725320),
         (0xcc045291),
         (0x3276d011),
         (0x4220da78),
         (0xbc5258f8),
         (0x8e245949),
         (0x7056dbc9)},
        {(0x00000000),
         (0x12010000),
         (0x53002000),
         (0x41012000),
         (0x20414000),
         (0x32404000),
         (0x73416000),
         (0x61406000),
         (0x20321e00),
         (0x32331e00),
         (0x73323e00),
         (0x61333e00),
         (0x00735e00),
         (0x12725e00),
         (0x53737e00),
         (0x41727e00)},
        {(0x3f800000),
         (0x3f890080),
         (0x3fa98010),
         (0x3fa08090),
         (0x3f9020a0),
         (0x3f992020),
         (0x3fb9a0b0),
         (0x3fb0a030),
         (0x3f90190f),
         (0x3f99198f),
         (0x3fb9991f),
         (0x3fb0999f),
         (0x3f8039af),
         (0x3f89392f),
         (0x3fa9b9bf),
         (0x3fa0b93f)},
        (0xfff80000),
        {0x0f,0xe8,0x93,0xf4,0x0e,0x7e,0xaf,0xe0,0x5d,0x20,
         0x58,0x92,0x06,0x7d,0x5b,0x05,0x6b,0x8a,0x33,0xbb,0x00}
    },
    {
        /* No.190 delta:1194 weight:821 */
        11213,
        72,
        11,
        16,
        {(0x00000000),
         (0x44a385c2),
         (0xca99870f),
         (0x8e3a02cd),
         (0x06c00be8),
         (0x42638e2a),
         (0xcc598ce7),
         (0x88fa0925),
         (0x00007bbf),
         (0x44a3fe7d),
         (0xca99fcb0),
         (0x8e3a7972),
         (0x06c07057),
         (0x4263f595),
         (0xcc59f758),
         (0x88fa729a)},
        {(0x00000000),
         (0x00527800),
         (0x302f0400),
         (0x307d7c00),
         (0x20017000),
         (0x20530800),
         (0x102e7400),
         (0x107c0c00),
         (0x10429e00),
         (0x1010e600),
         (0x206d9a00),
         (0x203fe200),
         (0x3043ee00),
         (0x30119600),
         (0x006cea00),
         (0x003e9200)},
        {(0x3f800000),
         (0x3f80293c),
         (0x3f981782),
         (0x3f983ebe),
         (0x3f9000b8),
         (0x3f902984),
         (0x3f88173a),
         (0x3f883e06),
         (0x3f88214f),
         (0x3f880873),
         (0x3f9036cd),
         (0x3f901ff1),
         (0x3f9821f7),
         (0x3f9808cb),
         (0x3f803675),
         (0x3f801f49)},
        (0xfff80000),
        {0xba,0xb7,0xd5,0xc8,0xec,0x6d,0xc1,0x0c,0x19,0x38,
         0x0f,0x27,0x93,0x3b,0xd8,0x74,0xa1,0x4e,0x45,0x89,0x00}
    },
    {
        /* No.191 delta:1166 weight:1045 */
        11213,
        40,
        11,
        9,
        {(0x00000000),
         (0x212f4af4),
         (0xf0432caf),
         (0xd16c665b),
         (0x11d00bf5),
         (0x30ff4101),
         (0xe193275a),
         (0xc0bc6dae),
         (0x0000755f),
         (0x212f3fab),
         (0xf04359f0),
         (0xd16c1304),
         (0x11d07eaa),
         (0x30ff345e),
         (0xe1935205),
         (0xc0bc18f1)},
        {(0x00000000),
         (0x08528000),
         (0x70084000),
         (0x785ac000),
         (0x00196000),
         (0x084be000),
         (0x70112000),
         (0x7843a000),
         (0x0024fe00),
         (0x08767e00),
         (0x702cbe00),
         (0x787e3e00),
         (0x003d9e00),
         (0x086f1e00),
         (0x7035de00),
         (0x78675e00)},
        {(0x3f800000),
         (0x3f842940),
         (0x3fb80420),
         (0x3fbc2d60),
         (0x3f800cb0),
         (0x3f8425f0),
         (0x3fb80890),
         (0x3fbc21d0),
         (0x3f80127f),
         (0x3f843b3f),
         (0x3fb8165f),
         (0x3fbc3f1f),
         (0x3f801ecf),
         (0x3f84378f),
         (0x3fb81aef),
         (0x3fbc33af)},
        (0xfff80000),
        {0xbf,0xbb,0x3f,0x17,0x6e,0x1a,0x3b,0x45,0x9b,0x2a,
         0x22,0x6a,0xf9,0xf5,0xba,0x97,0x3b,0x8a,0xec,0xd6,0x00}
    },
    {
        /* No.192 delta:4742 weight:1471 */
        11213,
        27,
        4,
        6,
        {(0x00000000),
         (0x7fe173b6),
         (0x51169f27),
         (0x2ef7ec91),
         (0x0a300c00),
         (0x75d17fb6),
         (0x5b269327),
         (0x24c7e091),
         (0x00001d00),
         (0x7fe16eb6),
         (0x51168227),
         (0x2ef7f191),
         (0x0a301100),
         (0x75d162b6),
         (0x5b268e27),
         (0x24c7fd91)},
        {(0x00000000),
         (0x41420000),
         (0x00010000),
         (0x41430000),
         (0x29040000),
         (0x68460000),
         (0x29050000),
         (0x68470000),
         (0x00009e00),
         (0x41429e00),
         (0x00019e00),
         (0x41439e00),
         (0x29049e00),
         (0x68469e00),
         (0x29059e00),
         (0x68479e00)},
        {(0x3f800000),
         (0x3fa0a100),
         (0x3f800080),
         (0x3fa0a180),
         (0x3f948200),
         (0x3fb42300),
         (0x3f948280),
         (0x3fb42380),
         (0x3f80004f),
         (0x3fa0a14f),
         (0x3f8000cf),
         (0x3fa0a1cf),
         (0x3f94824f),
         (0x3fb4234f),
         (0x3f9482cf),
         (0x3fb423cf)},
        (0xfff80000),
        {0xb4,0x7f,0x09,0x23,0xa8,0x3f,0x5e,0x9d,0x3e,0x97,
         0x19,0x94,0x35,0x0d,0xdb,0x3e,0xb7,0x43,0x15,0xd0,0x00}
    },
    {
        /* No.193 delta:3962 weight:1171 */
        11213,
        92,
        2,
        13,
        {(0x00000000),
         (0x76b1ced9),
         (0x928504d8),
         (0xe434ca01),
         (0x7b300c1a),
         (0x0d81c2c3),
         (0xe9b508c2),
         (0x9f04c61b),
         (0x0000b2d9),
         (0x76b17c00),
         (0x9285b601),
         (0xe43478d8),
         (0x7b30bec3),
         (0x0d81701a),
         (0xe9b5ba1b),
         (0x9f0474c2)},
        {(0x00000000),
         (0x08400000),
         (0x06400000),
         (0x0e000000),
         (0x01200000),
         (0x09600000),
         (0x07600000),
         (0x0f200000),
         (0x04c01e00),
         (0x0c801e00),
         (0x02801e00),
         (0x0ac01e00),
         (0x05e01e00),
         (0x0da01e00),
         (0x03a01e00),
         (0x0be01e00)},
        {(0x3f800000),
         (0x3f842000),
         (0x3f832000),
         (0x3f870000),
         (0x3f809000),
         (0x3f84b000),
         (0x3f83b000),
         (0x3f879000),
         (0x3f82600f),
         (0x3f86400f),
         (0x3f81400f),
         (0x3f85600f),
         (0x3f82f00f),
         (0x3f86d00f),
         (0x3f81d00f),
         (0x3f85f00f)},
        (0xfff80000),
        {0xdf,0x8e,0xab,0x00,0xdb,0xdc,0xe0,0x7a,0xb0,0x4c,
         0x05,0x23,0x25,0x58,0x15,0xac,0x74,0x97,0x22,0x21,0x00}
    },
    {
        /* No.194 delta:2215 weight:1499 */
        11213,
        63,
        26,
        4,
        {(0x00000000),
         (0xad5d413b),
         (0x166ef61d),
         (0xbb33b726),
         (0x9e000c20),
         (0x335d4d1b),
         (0x886efa3d),
         (0x2533bb06),
         (0x000077be),
         (0xad5d3685),
         (0x166e81a3),
         (0xbb33c098),
         (0x9e007b9e),
         (0x335d3aa5),
         (0x886e8d83),
         (0x2533ccb8)},
        {(0x00000000),
         (0x10720800),
         (0x60810000),
         (0x70f30800),
         (0x300c0000),
         (0x207e0800),
         (0x508d0000),
         (0x40ff0800),
         (0x20023e00),
         (0x30703600),
         (0x40833e00),
         (0x50f13600),
         (0x100e3e00),
         (0x007c3600),
         (0x708f3e00),
         (0x60fd3600)},
        {(0x3f800000),
         (0x3f883904),
         (0x3fb04080),
         (0x3fb87984),
         (0x3f980600),
         (0x3f903f04),
         (0x3fa84680),
         (0x3fa07f84),
         (0x3f90011f),
         (0x3f98381b),
         (0x3fa0419f),
         (0x3fa8789b),
         (0x3f88071f),
         (0x3f803e1b),
         (0x3fb8479f),
         (0x3fb07e9b)},
        (0xfff80000),
        {0x31,0xb8,0x64,0x9a,0xae,0xad,0xc8,0xf8,0x53,0x9b,
         0x64,0x2d,0x8c,0xb0,0xd2,0xa7,0x8e,0x2e,0xb2,0xc8,0x00}
    },
    {
        /* No.195 delta:1215 weight:463 */
        11213,
        60,
        13,
        19,
        {(0x00000000),
         (0x60520e47),
         (0xae8c1b90),
         (0xcede15d7),
         (0xc1c00c35),
         (0xa1920272),
         (0x6f4c17a5),
         (0x0f1e19e2),
         (0x0000fd60),
         (0x6052f327),
         (0xae8ce6f0),
         (0xcedee8b7),
         (0xc1c0f155),
         (0xa192ff12),
         (0x6f4ceac5),
         (0x0f1ee482)},
        {(0x00000000),
         (0x02e00000),
         (0x10cd1200),
         (0x122d1200),
         (0x14101800),
         (0x16f01800),
         (0x04dd0a00),
         (0x063d0a00),
         (0x0057be00),
         (0x02b7be00),
         (0x109aac00),
         (0x127aac00),
         (0x1447a600),
         (0x16a7a600),
         (0x048ab400),
         (0x066ab400)},
        {(0x3f800000),
         (0x3f817000),
         (0x3f886689),
         (0x3f891689),
         (0x3f8a080c),
         (0x3f8b780c),
         (0x3f826e85),
         (0x3f831e85),
         (0x3f802bdf),
         (0x3f815bdf),
         (0x3f884d56),
         (0x3f893d56),
         (0x3f8a23d3),
         (0x3f8b53d3),
         (0x3f82455a),
         (0x3f83355a)},
        (0xfff80000),
        {0x67,0xca,0xf4,0x9a,0x13,0x78,0x64,0xbc,0x60,0x6d,
         0x5b,0x5f,0x48,0x61,0x94,0x6f,0xdf,0x81,0x2c,0xe1,0x00}
    },
    {
        /* No.196 delta:939 weight:857 */
        11213,
        51,
        11,
        2,
        {(0x00000000),
         (0xa61e8990),
         (0x67d50999),
         (0xc1cb8009),
         (0xa3f00c49),
         (0x05ee85d9),
         (0xc42505d0),
         (0x623b8c40),
         (0x00000381),
         (0xa61e8a11),
         (0x67d50a18),
         (0xc1cb8388),
         (0xa3f00fc8),
         (0x05ee8658),
         (0xc4250651),
         (0x623b8fc1)},
        {(0x00000000),
         (0x08105000),
         (0x0002b000),
         (0x0812e000),
         (0x90084c00),
         (0x98181c00),
         (0x900afc00),
         (0x981aac00),
         (0x01007e00),
         (0x09102e00),
         (0x0102ce00),
         (0x09129e00),
         (0x91083200),
         (0x99186200),
         (0x910a8200),
         (0x991ad200)},
        {(0x3f800000),
         (0x3f840828),
         (0x3f800158),
         (0x3f840970),
         (0x3fc80426),
         (0x3fcc0c0e),
         (0x3fc8057e),
         (0x3fcc0d56),
         (0x3f80803f),
         (0x3f848817),
         (0x3f808167),
         (0x3f84894f),
         (0x3fc88419),
         (0x3fcc8c31),
         (0x3fc88541),
         (0x3fcc8d69)},
        (0xfff80000),
        {0x2c,0x79,0x20,0x40,0xe3,0x10,0xb7,0x11,0xcb,0xf4,
         0xba,0x4e,0xee,0x8b,0x5f,0x86,0xa9,0xdb,0x06,0x27,0x00}
    },
    {
        /* No.197 delta:1583 weight:1233 */
        11213,
        26,
        20,
        8,
        {(0x00000000),
         (0x33fe7f24),
         (0x88809fc9),
         (0xbb7ee0ed),
         (0x68600c55),
         (0x5b9e7371),
         (0xe0e0939c),
         (0xd31eecb8),
         (0x0000a12e),
         (0x33fede0a),
         (0x88803ee7),
         (0xbb7e41c3),
         (0x6860ad7b),
         (0x5b9ed25f),
         (0xe0e032b2),
         (0xd31e4d96)},
        {(0x00000000),
         (0x08750800),
         (0x82099800),
         (0x8a7c9000),
         (0x40e2b000),
         (0x4897b800),
         (0xc2eb2800),
         (0xca9e2000),
         (0x004a5e00),
         (0x083f5600),
         (0x8243c600),
         (0x8a36ce00),
         (0x40a8ee00),
         (0x48dde600),
         (0xc2a17600),
         (0xcad47e00)},
        {(0x3f800000),
         (0x3f843a84),
         (0x3fc104cc),
         (0x3fc53e48),
         (0x3fa07158),
         (0x3fa44bdc),
         (0x3fe17594),
         (0x3fe54f10),
         (0x3f80252f),
         (0x3f841fab),
         (0x3fc121e3),
         (0x3fc51b67),
         (0x3fa05477),
         (0x3fa46ef3),
         (0x3fe150bb),
         (0x3fe56a3f)},
        (0xfff80000),
        {0xb0,0x71,0x91,0x64,0xda,0x74,0xb8,0xc6,0x16,0x8c,
         0x5a,0x7e,0x26,0xea,0x75,0x3a,0x0b,0xa4,0xd9,0x97,0x00}
    },
    {
        /* No.198 delta:1565 weight:813 */
        11213,
        89,
        7,
        10,
        {(0x00000000),
         (0x98a83f1e),
         (0xcb779281),
         (0x53dfad9f),
         (0x20400c6a),
         (0xb8e83374),
         (0xeb379eeb),
         (0x739fa1f5),
         (0x000092fc),
         (0x98a8ade2),
         (0xcb77007d),
         (0x53df3f63),
         (0x20409e96),
         (0xb8e8a188),
         (0xeb370c17),
         (0x739f3309)},
        {(0x00000000),
         (0x467a0c00),
         (0x04059800),
         (0x427f9400),
         (0x01093800),
         (0x47733400),
         (0x050ca000),
         (0x4376ac00),
         (0xc0e0fe00),
         (0x869af200),
         (0xc4e56600),
         (0x829f6a00),
         (0xc1e9c600),
         (0x8793ca00),
         (0xc5ec5e00),
         (0x83965200)},
        {(0x3f800000),
         (0x3fa33d06),
         (0x3f8202cc),
         (0x3fa13fca),
         (0x3f80849c),
         (0x3fa3b99a),
         (0x3f828650),
         (0x3fa1bb56),
         (0x3fe0707f),
         (0x3fc34d79),
         (0x3fe272b3),
         (0x3fc14fb5),
         (0x3fe0f4e3),
         (0x3fc3c9e5),
         (0x3fe2f62f),
         (0x3fc1cb29)},
        (0xfff80000),
        {0x32,0x19,0xd6,0x71,0xe2,0x9d,0xf7,0x99,0x34,0xa0,
         0x5f,0x9a,0xcd,0x9f,0x42,0x5a,0x43,0x47,0xee,0xb2,0x00}
    },
    {
        /* No.199 delta:3921 weight:921 */
        11213,
        49,
        3,
        15,
        {(0x00000000),
         (0xbb75ffa8),
         (0xa0b33e2a),
         (0x1bc6c182),
         (0x9a200c78),
         (0x2155f3d0),
         (0x3a933252),
         (0x81e6cdfa),
         (0x000015a3),
         (0xbb75ea0b),
         (0xa0b32b89),
         (0x1bc6d421),
         (0x9a2019db),
         (0x2155e673),
         (0x3a9327f1),
         (0x81e6d859)},
        {(0x00000000),
         (0x45540800),
         (0x07840400),
         (0x42d00c00),
         (0x08200000),
         (0x4d740800),
         (0x0fa40400),
         (0x4af00c00),
         (0x02201e00),
         (0x47741600),
         (0x05a41a00),
         (0x40f01200),
         (0x0a001e00),
         (0x4f541600),
         (0x0d841a00),
         (0x48d01200)},
        {(0x3f800000),
         (0x3fa2aa04),
         (0x3f83c202),
         (0x3fa16806),
         (0x3f841000),
         (0x3fa6ba04),
         (0x3f87d202),
         (0x3fa57806),
         (0x3f81100f),
         (0x3fa3ba0b),
         (0x3f82d20d),
         (0x3fa07809),
         (0x3f85000f),
         (0x3fa7aa0b),
         (0x3f86c20d),
         (0x3fa46809)},
        (0xfff80000),
        {0x86,0x1e,0x5b,0xa8,0x8e,0x65,0x6f,0xc0,0xbc,0x92,
         0x81,0x1c,0x6d,0xbd,0x0f,0xd4,0x7e,0x57,0x5f,0x75,0x00}
    }
};

#endif


